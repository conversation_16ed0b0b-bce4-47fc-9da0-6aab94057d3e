import React, { useState } from 'react';
import { Box, Typography, Button, Paper, Container } from '@mui/material';
import ContentBlock from '../components/LMS/course-content/ContentBlock';

const TestContentBlockForm = () => {
  const [formSubmitted, setFormSubmitted] = useState(false);

  // Test form data - verdiğiniz örnek data
  const testBlockData = {
    _id: "6828ff7ccc17d17c495944eb",
    type: "form",
    title: "Support & Contact us form",
    description: "Platform support & contact us form elements.",
    form_id: "6828ff7ccc17d17c495944eb",
    form_data: {
      _id: "6828ff7ccc17d17c495944eb",
      title: "Support & Contact us form",
      description: "Platform support & contact us form elements.",
      topics: [
        {
          title: "Contact us",
          description: "",
          fields: [
            {
              _id: "67d021809f916952ffc49436",
              label: "Name and surname",
              description: "",
              name: "name",
              type: "text",
              required: true,
              conditional_logic: {
                enabled: false,
                rules: [
                  [
                    {
                      field: "",
                      operator: "has_any_value",
                      value: "",
                      _id: "67d021809f916952ffc49438"
                    }
                  ]
                ],
                _id: "67d021809f916952ffc49437"
              },
              validation: {
                min: 3,
                max: 150,
                pattern: "",
                message: "Write your name and surname"
              },
              fields: [],
              options: []
            },
            {
              _id: "67d021809f916952ffc49439",
              label: "Email",
              description: "",
              name: "email",
              type: "email",
              required: true,
              conditional_logic: {
                enabled: false,
                rules: [
                  [
                    {
                      field: "",
                      operator: "has_any_value",
                      value: "",
                      _id: "67d021809f916952ffc4943b"
                    }
                  ]
                ],
                _id: "67d021809f916952ffc4943a"
              },
              validation: {
                min: null,
                max: null,
                pattern: "",
                message: "Please enter a valid email address"
              },
              fields: [],
              options: []
            },
            {
              _id: "67d021809f916952ffc4943c",
              label: "Subject",
              description: "",
              name: "subject",
              type: "select",
              required: true,
              conditional_logic: {
                enabled: false,
                rules: [
                  [
                    {
                      field: "",
                      operator: "has_any_value",
                      value: "",
                      _id: "67d021809f916952ffc49443"
                    }
                  ]
                ],
                _id: "67d021809f916952ffc49442"
              },
              validation: {
                min: null,
                max: null,
                pattern: "",
                message: ""
              },
              fields: [],
              options: [
                {
                  _id: "67d021809f916952ffc4943d",
                  label: "Content-related",
                  value: "content-related"
                },
                {
                  _id: "67d021809f916952ffc4943f",
                  label: "Technical problem",
                  value: "technical-problem"
                },
                {
                  _id: "67d021809f916952ffc49440",
                  label: "Personal data",
                  value: "personal-data"
                },
                {
                  _id: "67d021809f916952ffc49441",
                  label: "Other",
                  value: "other"
                }
              ]
            },
            {
              _id: "67d021809f916952ffc49444",
              label: "Details",
              description: "",
              name: "details",
              type: "textarea",
              required: true,
              conditional_logic: {
                enabled: false,
                rules: [
                  [
                    {
                      field: "",
                      operator: "has_any_value",
                      value: "",
                      _id: "67d021809f916952ffc49446"
                    }
                  ]
                ],
                _id: "67d021809f916952ffc49445"
              },
              validation: {
                min: null,
                max: null,
                pattern: "",
                message: ""
              },
              fields: [],
              options: []
            },
            // File upload field ekliyoruz
            {
              _id: "test-file-field-1",
              label: "Upload File",
              description: "Upload a file to support your request",
              name: "support_file",
              type: "file",
              required: false,
              file_settings: {
                multiple: false,
                max_size: 50,
                allowed_types: ['image/*', 'application/pdf', '.doc', '.docx', '.txt']
              },
              conditional_logic: {
                enabled: false,
                rules: []
              },
              validation: {
                min: null,
                max: null,
                pattern: "",
                message: ""
              },
              fields: [],
              options: []
            }
          ],
          _id: "67d021809f916952ffc49435"
        }
      ]
    }
  };

  const handleFormSubmit = (blockId) => {
    console.log('Form submitted for block:', blockId);
    setFormSubmitted(true);
  };

  const handleContentCompleted = () => {
    console.log('Content completed');
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          ContentBlock Form Test
        </Typography>
        
        <Typography variant="body1" sx={{ mb: 3 }}>
          Bu sayfa ContentBlock component'inin form özelliklerini test eder. File upload dahil.
        </Typography>

        {formSubmitted && (
          <Box sx={{ mb: 3, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
            <Typography color="success.dark">
              ✅ Form başarıyla gönderildi!
            </Typography>
          </Box>
        )}

        <ContentBlock
          block={testBlockData}
          courseId="test-course"
          chapterIndex={0}
          topicIndex={0}
          onFormSubmit={handleFormSubmit}
          onContentCompleted={handleContentCompleted}
        />

        <Box sx={{ mt: 3 }}>
          <Button 
            variant="outlined" 
            onClick={() => setFormSubmitted(false)}
          >
            Reset Test
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default TestContentBlockForm;
