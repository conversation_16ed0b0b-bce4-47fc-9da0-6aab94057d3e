import React, { useState } from 'react';
import { <PERSON>, Typo<PERSON>, Button, Paper, Container } from '@mui/material';
import FormRender from '../components/FormRender/FormRender';

const TestFileUpload = () => {
  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});

  // Test form data with file field
  const testFormData = {
    _id: 'test-file-form',
    title: 'File Upload Test Form',
    description: 'Test form for file upload functionality',
    topics: [
      {
        _id: 'topic-1',
        title: 'File Upload Test',
        description: 'Upload files to test the functionality',
        fields: [
          {
            _id: 'field-1',
            name: 'test_file_single',
            label: 'Upload Single File',
            type: 'file',
            description: 'Upload a single file',
            required: false,
            file_settings: {
              multiple: false,
              max_size: 50,
              allowed_types: ['image/*', 'application/pdf', '.doc', '.docx', '.txt']
            },
            conditional_logic: {
              enabled: false,
              rules: []
            }
          },
          {
            _id: 'field-2',
            name: 'test_file_multiple',
            label: 'Upload Multiple Files',
            type: 'file',
            description: 'Upload multiple files',
            required: false,
            file_settings: {
              multiple: true,
              max_size: 50,
              allowed_types: ['image/*', 'application/pdf', '.doc', '.docx', '.txt']
            },
            conditional_logic: {
              enabled: false,
              rules: []
            }
          },
          {
            _id: 'field-3',
            name: 'test_text',
            label: 'Text Field',
            type: 'text',
            description: 'A simple text field',
            required: true,
            conditional_logic: {
              enabled: false,
              rules: []
            }
          }
        ]
      }
    ]
  };

  const handleFormChange = (data) => {
    console.log('Form data changed:', data);
    
    if (data.__errors) {
      setFormErrors(prev => ({ ...prev, ...data.__errors }));
    } else {
      // Remove __errors and other meta fields
      const { __errors, __formState, __userInteracted, ...cleanData } = data;
      setFormData(prev => ({ ...prev, ...cleanData }));
    }
  };

  const handleSubmit = () => {
    console.log('Submitting form data:', formData);
    
    // Create form response data
    const responses = Object.keys(formData).map(fieldName => {
      const field = testFormData.topics[0].fields.find(f => f.name === fieldName);
      return {
        fieldId: field._id,
        name: fieldName,
        type: field.type,
        value: formData[fieldName]
      };
    });

    const formResponse = {
      formId: testFormData._id,
      formType: 'test',
      title: testFormData.title,
      description: testFormData.description,
      responses
    };

    console.log('Form response to submit:', formResponse);
    alert('Check console for form response data');
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          File Upload Test Page
        </Typography>
        
        <Typography variant="body1" sx={{ mb: 3 }}>
          This page tests the file upload functionality in FormRender component.
        </Typography>

        <FormRender
          formData={testFormData}
          onChange={handleFormChange}
          values={formData}
          errors={formErrors}
        />

        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button variant="contained" onClick={handleSubmit}>
            Submit Form
          </Button>
          <Button 
            variant="outlined" 
            onClick={() => {
              setFormData({});
              setFormErrors({});
            }}
          >
            Reset Form
          </Button>
        </Box>

        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Current Form Data:
          </Typography>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '16px', 
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '12px'
          }}>
            {JSON.stringify(formData, null, 2)}
          </pre>
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography variant="h6" gutterBottom>
            Form Errors:
          </Typography>
          <pre style={{ 
            background: '#ffebee', 
            padding: '16px', 
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '12px'
          }}>
            {JSON.stringify(formErrors, null, 2)}
          </pre>
        </Box>
      </Paper>
    </Container>
  );
};

export default TestFileUpload;
