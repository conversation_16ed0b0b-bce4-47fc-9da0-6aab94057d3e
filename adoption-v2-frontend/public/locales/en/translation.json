{"header": {"home": "Home", "training": "Train", "create": "Create", "innovate": "Innovate", "cockpit": "Cockpit", "aiUseCases": "AI Use Cases", "apply": "Apply", "language": "Language", "signOut": "Sign out", "accountSettings": "Account settings", "help": "Help", "logo": "AIBS Logo", "contactUs": "Contact us", "supportFormNotFound": "Support form not found. Please try again later.", "submittingRequest": "Submitting your request..."}, "journey": {"limitedUser": {"beginnerCompleted": "You have completed the Beginner journey.", "accessWarning": "Once approved by your company, you can continue to the next level. We therefore ask you for a little patience.", "lockTooltip": "Once approved by your company, you can continue to the next level. We therefore ask you for a little patience."}}, "roleUpgradeModal": {"title": "Your account has been upgraded", "accountUpgraded": "We are very happy to inform you that your company has upgraded your account on the AI Business School’s “AI Adoption Platform”.", "extendedAccess": "You have now extended access to all relevant training journeys, courses, tools and applications. We recommend, that you continue by starting your job-specific, structured training journey on “expert-level”.", "understand": "I understand"}, "home": {"welcome": "Welcome {{name}}", "jobRole": "Your department: {{role}} - Your job role: {{jobRole}}", "jobRolePrefix": "Your job role or specialization: ", "trainingJourneys": "Your role-specific training journeys", "selectLevel": "Select level", "skipJourney": "Skip entire journey »", "skipJourneyModal": {"description": "Based on the answers you gave during onboarding, we understand that you already have a solid foundation of knowledge in AI. If you'd like, you can skip the Beginner Journey and jump straight into the Expert Journey."}, "levels": {"beginner": "<PERSON><PERSON><PERSON>", "expert": "Expert", "master": "Master"}, "journeyCard": {"completed": "Completed", "playNow": "Play now", "locked": "Locked"}, "dailyAI": {"title": "AI application made easy", "subtitle": "Discover and apply AI use cases specifically selected for you"}, "aiPlaygrounds": {"title": "Most popular AI playgrounds", "subtitle": "Explore some of the most cutting-edge AI technologies"}, "techCompanies": {"title": "AI ecosystems by leading tech companies", "subtitle": "Explore AI tools, training programs and interactive playgrounds of leading tech firms"}, "watchIntroVideo": "Platform video", "PlatformVideoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_Final_AI_ADOPTION_Platform_180425EN1_V03_vf.mp4/Video_Final_AI_ADOPTION_Platform_180425EN1_V03_vf.m3u8", "introVideo": {"title": "Platform Video"}}, "common": {"lockedTooltip": "This part is not included in your current subscription - please contact your AI Business School Account Manager for more information.", "loading": "Loading training content...", "error": "An error occurred", "congratulations": "Congratulations!", "continue": "Continue", "unknownError": "Unknown error", "retry": "Retry", "tryAgain": "Please try again", "locked": "This training is locked", "completed": "Training completed", "inProgress": "In progress", "startNow": "Start now", "pleaseLogin": "Please log in to access this page.", "addToFavorites": "Add to favorites", "myFavorite": "My favorite", "addedToFavorites": "Added to favorites", "removedFromFavorites": "Removed from favorites", "goodJob": "Good job!", "points": "Points!", "back": "Back", "backToApply": "Back to Apply", "backToToolkit": "Back to Toolkit", "backToPlayground": "Back to Playground", "errorLoading": "An error occurred while loading the use cases. Please try again later.", "shortcutNotFound": "Shortcut not found", "fieldRequired": "This field is required", "fieldCannotBeEmpty": "This field cannot be empty", "pleaseCompleteRequiredFields": "Please complete all required fields.", "signInWithMicrosoftAccount": "Sign in with Microsoft Account", "validation": {"required": "This field is required", "fieldCannotBeEmpty": "This field cannot be empty"}, "warnings": {"dataPrivacy": "This app uses AI. Check for mistakes. Please refrain from sharing any confidential business data—including customer, product, or employee information—with AI during exercises to ensure sensitive data remains secure."}, "errors": {"usageLimitExceeded": "You have exceeded the usage limit, please contact your administrator.", "textGenerationFailed": "Text could not be generated. Please try again.", "imageGenerationFailed": "Image could not be generated. Please try again.", "videoGenerationFailed": "Video could not be generated. Please try again."}, "errorOccurred": "An error occurred!", "thankYou": "Thank you!", "submitting": "Submitting...", "submit": "Submit", "true": "True", "false": "False", "workflows": {"title": "Discover more {{role}} workflows", "discover": "Discover our AI-powered workflows", "showAll": "Show all workflows", "run": {"editApp": "Edit app", "restartApp": "Restart app", "errorLoading": "Error loading workflow", "next": "Next"}}, "copy": "Copy to clipboard", "copied": "Copied!", "result": "Result", "showLess": "Show less", "showMore": "Show more"}, "managementUseCases": {"title": "Management related AI use cases and apps", "description": "Discover carefully selected, relevant AI use cases and apps for your daily management routines"}, "itUseCases": {"title": "IT related use cases", "description": "Discover carefully selected, relevant IT use cases for your organization."}, "footer": {"privacyPolicy": "Privacy policy"}, "privacyPolicy": {"title": "Privacy Policy", "subtitle": "Learn how we collect, use, and protect your personal information. Last updated: 06 September 2024"}, "termsAndConditions": {"title": "Terms and Conditions", "subtitle": "Please read these terms and conditions carefully before using our services. Last updated: 08 August 2023"}, "modal": {"next": "Next", "previous": "Previous", "complete": "Complete", "completed": "Completed", "done": "Done", "close": "Close", "cancel": "Cancel", "confirm": "Confirm", "watchVideoFirst": "Please finish watching the video first.", "continueTraining": "Continue training", "completeTraining": "Complete training", "back": "Back", "submit": "Submit", "continue": "Continue", "review": "Review", "reviewYourSelections": "Review your selections."}, "training": {"title": "Your job-specific training journeys", "recommended": "Further recommended trainings", "recommendedSubtitle": "Trainings recommended for your personal role and background", "currentLevel": {"prefix": "Your current level:"}, "progress": {"title": "Training Progress", "stepsCompleted": "{{completed}} out of {{total}} steps completed", "currentStep": "Current step", "stepCompleted": "You've completed this step", "allStepsCompleted": "All steps completed"}, "topicSpecific": {"title": "Topic-specific trainings", "subtitle": "Advance knowledge with specialized AI training programs tailored for targeted learning and mastery"}, "technical": {"title": "Technical AI trainings", "subtitle": "Advance knowledge with specialized AI training programs tailored for targeted learning and mastery"}, "toolSpecific": {"title": "Tool-specific trainings", "subtitle": "Explore AI tools, training programs and interactive playgrounds of leading tech firms"}, "technicalSpecialists": {"title": "Trainings for Technical Specialists", "subtitle": "Trainings recommended for technical specialists"}, "common": {"loading": "Loading training content...", "error": "An error occurred while loading training content.", "retry": "Retry", "locked": "This training is locked", "completed": "Training completed", "inProgress": "In progress", "startNow": "Start now"}}, "apply": {"title": "Apply", "subtitle": "Discover job-specific AI use cases, apps and technologies", "discoverMore": {"useCases": "Discover more {{role}} use cases", "showAll": "Show all use cases", "specialSelection": "Special selection for you", "workflows": "Discover more {{role}} workflows"}, "management": {"title": "Discover management related AI use cases & apps", "subtitle": "Special selection of AI use cases and apps for your daily management routines"}, "it": {"title": "Discover IT related AI use cases & apps", "subtitle": "Special selection for you"}, "aiPlaygrounds": {"title": "Most popular AI Playgrounds", "subtitle": "Explore some of the most cutting-edge AI technologies"}, "favorites": {"title": "My favorite use cases, apps and workflows", "subtitle": "That I have marked as favorites", "addToFavorites": "Add to favorites", "myFavorite": "My favorite", "addedToFavorites": "Added to favorites", "removedFromFavorites": "Removed from favorites"}, "techCompanies": {"title": "AI ecosystems provided by leading tech firms", "subtitle": "Explore AI tools, training programs and interactive playgrounds of leading tech firms"}, "common": {"loading": "Loading content...", "error": "An error occurred while loading content.", "retry": "Retry", "locked": "This feature is locked", "completed": "Completed", "inProgress": "In progress", "startNow": "Start now", "viewMore": "View more", "usedTimes": "Used {{count}} times"}, "workflows": {"title": "Discover {{role}} AI workflows", "subtitle": "Discover AI-powered agentic workflows", "showAll": "Show all workflows", "run": {"editApp": "Edit app", "restartApp": "Restart app", "errorLoading": "Error loading workflow"}}}, "create": {"title": "Creator tools", "subtitle": "Create your own use cases, simple apps, workflows and also advanced AI solutions.", "simpleTools": {"title": "Simple creator tools for everyone", "subtitle": "Easy-to-use tools for creating AI solutions"}, "advancedTools": {"title": "More advanced creator tools", "subtitle": "Advanced tools for experienced users"}, "buttons": {"startNow": "Start now"}, "common": {"loading": "Loading creator tools...", "error": "An error occurred while loading tools.", "retry": "Retry", "locked": "This tool is locked", "completed": "Tool setup completed", "inProgress": "In progress", "startNow": "Start now", "viewMore": "View more", "usedTimes": "Used {{count}} times"}, "workflowCreator": {"header": {"loadSample": "Load Sample Workflow", "saveDraft": "Save Draft", "saveAndRun": "Save & Run", "workflowNamePlaceholder": "What is the name of your flow?", "loadSampleTooltip": "Load a sample workflow to explore an example flow and gain insights for creating your own."}, "tour": {"steps": {"appName": {"title": "Workflow Name", "content": "Start by giving your workflow a descriptive name. This will help you identify it later."}, "headerButtons": {"title": "Action Buttons", "content": "Use these buttons to load a sample workflow, save your work as a draft, or save and run your completed workflow."}, "componentsArea": {"title": "Components Panel", "content": "This panel contains all the components you can use to build your workflow. Drag and drop them to the workflow area."}, "dropArea": {"title": "Workflow Area", "content": "Drag and drop components here to build your workflow. You can arrange them in the order you want them to execute."}}}, "components": {"title": "Components", "textInput": "Text Input", "display": "Display", "prompt": "Prompt"}, "alerts": {"enterAppName": "Please enter an app name.", "createWorkflow": "You must create a workflow before you can save and run it.", "includeDisplay": "You must include a Display Component at the end of your workflow for proper run.", "fillInputs": "Please fill in all required input fields.", "fillOutputNames": "Please fill in all required output names for prompts.", "includeVariable": "You must include a variable in the Prompt.", "maxDisplay": "You can add up to 1 Display Component.", "removeExisting": "Before loading the Sample Workflow, you must remove all existing Components", "noEditSample": "Cannot load sample workflow in edit mode"}, "dialog": {"creating": "Creating your app...", "updating": "Updating your app...", "buildingMessage": "We are building your app, please give us a few seconds", "updatingMessage": "We are updating your app, please give us a few seconds"}, "settings": {"title": "Parameter Settings", "temperature": "Temperature", "topP": "Top P", "presencePenalty": "Presence penalty", "frequencyPenalty": "Frequency penalty", "tooltips": {"temperature": "What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. We generally recommend altering this or top_p but not both.", "topP": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered. We generally recommend altering this or temperature but not both.", "frequencyPenalty": "Number between 0 and 2.0. Values penalize new tokens based on their existing frequency in the text so far, decreasing the model's likelihood to repeat the same line verbatim.", "presencePenalty": "Number between 0 and 2.0. Values penalize new tokens based on whether they appear in the text so far, increasing the model's likelihood to talk about new topics."}}, "componentsColumn": {"title": "Components", "tooltips": {"textInput": "Add text input fields to collect user information that can be used in your prompts.", "display": "Show the final results of your workflow. You can display text, variables, or any other content generated by your prompts.", "prompt": "Use variables from text inputs to generate dynamic content. You can include multiple variables in a single prompt."}}, "dropArea": {"emptyState": "Drag and drop components", "form": {"input": "Input", "inputDetail": "Input detail", "outputName": "Output name", "prompt": "Prompt"}, "variables": {"promptTitle": "Prompt variables:", "displayTitle": "Display variables:", "displaySubtitle": "Choose the topic titles you want to be displayed in the end result."}}}}, "innovate": {"title": "Innovate", "subtitle": "Share your own ideas and solutions.", "buttons": {"shareIdeas": "Share ideas"}, "common": {"loading": "Loading innovation tools...", "error": "An error occurred while loading tools.", "retry": "Retry", "locked": "This feature is locked", "completed": "Idea submitted successfully", "inProgress": "In progress", "startNow": "Start now", "viewMore": "View more", "usedTimes": "Used {{count}} times"}, "form": {"title": "Share Your Innovation", "description": "Tell us about your innovative idea or solution", "submit": "Submit Idea", "cancel": "Cancel"}}, "tools": {"openai": {"title": "OpenAI Tools", "description": "Learn how you can use OpenAI tools more efficiently."}, "microsoft": {"title": "Microsoft's AI Tools", "description": "Here you get access to trainings and playgrounds for the full spectrum of Microsoft's relevant AI tools and products.", "trainingSteps": {"chatgptIntro": "Introduction to ChatGPT"}, "top10Copilot": {"title": "Top 10 to try first with <PERSON><PERSON><PERSON>", "description": "Discover the most essential features to get started with Microsoft Copilot."}, "promptingCheatSheet": {"title": "Prompting cheat sheet", "description": "Learn effective prompting techniques for Microsoft Copilot."}, "microsoft365ToolGuidelinesForCopilot": {"title": "Microsoft 365 tool guidelines for Copilot", "description": "Get started with Microsoft 365 Copilot"}}, "githubCopilot": {"title": "GitHub Copilot", "description": "Embark on an immersive journey with our interactive platform, engineered to showcase the transformative power of GitHub Copilot's AI-driven innovation. Tackle practical challenges in different programming languages to refine your coding expertise and revolutionize your pair programming experience.<ul><li><strong>Multi-language support:</strong> JavaScript &amp; Python examples</li><li><strong>AI model comparison:</strong> Compare capabilities of different AI models available in GitHub Copilot</li><li><strong>Practical challenges:</strong> 8 core challenges and 5 AI model comparison categories</li><li><strong>Safe environment:</strong> Pre-configured GitHub Codespace ready for experimentation</li></ul>", "tryItButton": "Try it now", "benefits": {"title": "Benefits", "beginners": {"title": "For GitHub Copilot beginners:", "description": "Learn how to effectively use GitHub Copilot and start to practically apply and integrate it into your daily coding work."}, "advanced": {"title": "For GitHub Copilot advanced users:", "description1": "Get access to the most recent AI models, tips, and tricks to expand your capabilities.", "description2": "Leverage GitHub Copilot to automate code creation based on functional and technical specifications."}}, "highlights": {"title": "Key Highlights", "intro": "Even if you don't have an enterprise GitHub Copilot license, you can use the playground with GitHub Copilot Free, which is offered for free by GitHub.", "setup": "To use Copilot Free, you first have to activate Copilot Free in your GitHub account settings.", "activation": {"step1": "In the upper-right corner of any page on GitHub, click your profile photo, then click Your Copilot.", "step2": "Click Start using Copilot Free to activate Copilot Free and open a conversation thread.", "step3": "Click Use Copilot in other IDEs and follow the instructions for your IDE."}, "with": "With GitHub Copilot Free you can do the following things:", "completions": {"title": "2,000 intelligent code completions a month:", "description": "Get context-aware code suggestions that draw context from your GitHub projects and VS Code workspace."}, "chat": {"title": "50 Copilot Chat messages a month:", "description": "Ask Copilot for help understanding code, refactoring something, or debugging an issue."}, "model": {"title": "Choose your AI model:", "description": "Pick between OpenAI GPT-4o, Google Gemini 2.0 Flash, or Claude 3.5 Sonnet."}}, "inAction": {"title": "GitHub Copilot in Action", "imageAlt": "GitHub Copilot Code Example"}, "environment": {"title": "Pre-Configured Development Environment", "description": "GitHub Codespaces will power the Playground, offering a fully managed, cloud-based environment tailored for Copilot exploration.<ul><li>Microsoft Visual Studio Code Online will serve as the user interface, ensuring a familiar and robust coding experience.</li><li>The GitHub Copilot extension will be pre-installed and pre-configured, allowing users to start using Copilot immediately without additional setup.</li></ul>"}, "challenges": {"title": "Scenario-Based Challenges", "description": "Predefined coding tasks to demonstrate Copilot's utility in real-world situations, such as:<ul><li>Solving algorithmic problems.</li><li>Writing and optimizing unit tests.</li><li>Working with popular frameworks like React.</li><li>Instant feedback highlighting Copilot's contributions to the solution.</li></ul>"}, "trainingProgress": {"currentStepTitle": "Introduction to GitHub Copilot"}}, "aiPlanet": {"title": "AI Planet Tools", "description": "Learn how to leverage some of the most powerful AI tools to create sophisticated AI apps, chatbots, workflows and agents"}, "aiBusinessSchool": {"title": "AI Business School Tools", "description": "Learn how you can optimally apply AI Business School's own AI tools"}, "toolkits": {"title": "Downloadable toolkits & resources", "description": "Practical resources, including prompting cheat sheets, Microsoft 365 Copilot tool guidelines for Word, PowerPoint, Teams, and starter toolkits for setting up Copilot at your organization."}}, "accountSettings": {"title": "Account settings", "subtitle": "You can change some of your personal account data here directly. Some other data fields cannot be changed without contacting us. In those cases please contact us via the \"Help\" section.", "tabs": {"basicInfo": "Basic information", "personalInfo": "Personal information", "language": "Language"}, "userProfile": {"title": "User profile", "firstName": "First name", "lastName": "Last name", "saveChanges": "Save changes", "saving": "Saving..."}, "passwordChange": {"title": "Change your password", "currentPassword": "Current password", "newPassword": "New password", "confirmPassword": "Confirm new password", "veryWeak": "Very Weak", "weak": "Weak", "medium": "Medium", "strong": "Strong", "passwordStrength": "Password Strength", "lowercaseUppercase": "At least one lowercase & one uppercase letter", "number": "At least one number (0-9)", "specialChar": "At least one special character (!@#$%^&*.)", "minLength": "At least 8 characters", "passwordsNotMatch": "Passwords do not match"}, "resetModal": {"title": "Reset user data", "description": "This action will reset the current user information.", "deleting": "Deleting...", "onboardData": "Onboarding data", "journeyData": "Journey data", "resetButton": "Reset user data", "userData": "User data"}, "personalInfo": {"industry": {"label": "In which industry are you interested in?", "placeholder": "Select industry"}, "language": {"label": "What is your preferred language?"}, "businessFunction": {"label": "In which business function do you work?"}, "businessJobRole": {"label": "What is your job role or specialization?"}, "managementRole": {"label": "Are you currently in a management role?"}, "technicalBackground": {"label": "How would you consider yourself in terms of your technical background?"}, "aiKnowledge": {"label": "How would you rate your current level of AI knowledge and experience?"}, "disabledFieldTooltip": "The responses you provided during the onboarding process are final and cannot be modified later."}}, "notifications": {"userUpdated": "Your information has been successfully updated.", "passwordChanged": "Password successfully changed", "error": "An error occurred", "userDataReset": "User data successfully reset"}, "cockpit": {"welcome": {"title": "Personal cockpit", "description": "Unified dashboard for a comprehensive overview of your achievements."}, "statistics": {"appliedTotalUseCases": "Applied total use cases", "createdApp": "Created apps", "createdWorkflow": "Created workflows", "appliedUniqueUseCases": "Different use cases applied", "submittedIdeas": "Submitted ideas", "obtainedCertificates": "Obtained certificates", "completedTrainingJourneys": "Completed training journeys"}, "common": {"times": "times", "loading": "Loading...", "mostFrequentlyUsedUseCases": "My most frequently used use cases", "specialSelectionForYou": "Special selection for you", "noFavorites": "You have no favorites yet.", "noMostUsedApps": "You have not used any apps yet."}, "badges": {"New": "New"}, "actions": {"View certificate": "View certificate"}, "items": {"AI adoption survey": "AI adoption survey", "Master journey assessment": "Master journey assessment", "Test Project": "Test Project", "Job-specific use case training for marketing": "Job-specific use case training for marketing", "Human face generator": "Human face generator", "Equipment calibration assistant": "Equipment calibration assistant", "E-mail optimizer": "E-mail optimizer", "Translator GPT": "Translator GPT", "Maintenance schedule optimizer": "Maintenance schedule optimizer", "AI Beginner in IT, AI & Data assessment & certificate": "AI Beginner in IT, AI & Data assessment & certificate", "Successfully navigate AI risks": "Successfully navigate AI risks"}, "tabs": {"myTrainings": "My trainings", "topUseCaseApps": "Top use case apps", "createdApps": "Created apps", "ideasSubmitted": "Ideas submitted", "certificates": "Certificates"}, "trainingTabs": {"inProgress": "In progress", "completed": "Completed", "goToTraining": "Go to training", "noInProgressCourses": "You have no courses in progress.", "noCompletedCourses": "You have not completed any courses yet."}, "createdApps": {"title": "My created apps", "subtitle": "Apps that I have created"}, "submittedIdeas": {"title": "My submitted ideas", "subtitle": "Ideas that I have submitted", "viewDetails": "View Details", "created": "Created:"}, "levelUp": {"message": "To gain more points & level up, apply & create more use cases, workflows, share more ideas!"}, "certificates": {"title": "My certificates", "subtitle": "Certificates that I have obtained", "training": "Training", "date": "Date", "view": "View", "share": "Share", "noCertificates": "You have not obtained any certificates yet.", "levelUp": {"message": "To gain more points & level up, apply & create more use cases, workflows, share more ideas!"}}}, "tutorials": {"simpleAIAppCreator": {"title": "Simple AI App Creator tutorial", "description": "Learn how to create customized use case apps tailored to your specific business requirements.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video+En-Use+Case+App+Creator+July+22Nd-Vf.mp4/Video+En-Use+Case+App+Creator+July+22Nd-Vf.m3u8", "completedMessage": "Video completed", "pleaseLogin": "Please log in to access this tutorial.", "backToPlayground": "Back to Playground"}, "agenticWorkflowCreator": {"title": "Agentic Workflow Creator tutorial", "description": "Explore how to create agentic workflows that automates complex tasks with AI, completing multi-step processes autonomously and enhancing GPT model performance iteratively.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_EN_+Workflowcreator_7th+August_EN_vf.mp4/Video_EN_+Workflowcreator_7th+August_EN_vf.m3u8", "completedMessage": "Video completed", "pleaseLogin": "Please log in to access this tutorial.", "backToPlayground": "Back to Playground"}, "advancedGenAICreator": {"title": "Advanced AI Creator tutorial", "description": "Explore the powerful AI tool and its endless possibilities. Learn how to create a translation chatbot with this step by step tutorial.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F12-11+%5BUPD%5D+__+%5BEN%5D-Advanced-GenAI-Tutorial-Audio-Patch-%5BPEREMECI%5D+(1).mp4/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F12-11+%5BUPD%5D+__+%5BEN%5D-Advanced-GenAI-Tutorial-Audio-Patch-%5BPEREMECI%5D+(1).m3u8", "completedMessage": "Video completed", "pleaseLogin": "Please log in to access this tutorial.", "backToPlayground": "Back to Playground"}, "heygen": {"title": "Introduction to <PERSON><PERSON><PERSON>", "description": "Learn how to create personalized videos with HeyGen, a powerful AI video production tool.", "pleaseLogin": "Please log in to access this tutorial.", "howToUseAvatars": "How to Use Public Avatars in HeyGen", "welcomeText": "Welcome to HeyGen Tutorials made by HeyGen! Get ready to dive into the exciting world of avatars and uncover the secrets behind them. In this tutorial, we'll walk you through the process of selecting a public avatar and creating an amazing video, step-by-step.", "meetNewestAvatars": "Meet HeyGen's Newest Avatars", "meetHeygenText": "Meet HeyGen, next-gen video creation platform turns your text into professional spokesperson video just in minutes!", "howWeCreated": "How we created an AI Avatar!", "howWeCreatedText": "This video showcases the process of bringing these AI Avatars to life, from carefully selecting and preparing the actors to capturing the intricate details of human expressions and movements. We record every nuance to bring you the most lifelike digital avatars!"}, "spesific_sales": {"title": "Specific Sales use cases", "description": "Real-world examples of how different Sales roles—from field sellers to enablement managers—can use Copilot to streamline daily tasks.", "backToPlayground": "Back to Playground", "pdfUrl": "https://aibs-content.s3.eu-central-2.amazonaws.com/Specific+Sales+use+cases_scenario+library.pdf"}}, "onboarding": {"steps": {"welcome": "Welcome", "changePassword": "Change password", "language": "What is your preferred language?", "businessFunction": "In which business function do you work?", "jobRole": "What is your job role or specialization?", "managementRole": "Are you currently in a management role?", "technicalBackground": "How would you consider yourself in terms of your technical background?", "aiExperience": "How would you rate your current level of AI knowledge and experience?", "industry": "In which industry are you interested in?", "termsAndConditions": "Terms and Conditions", "review": {"title": "Review your selections", "description": "Your preferences cannot be modified later. You can view your selections on the Account Settings page."}, "of": "of"}, "welcome": {"title": "Welcome {{username}}", "description": "Personalize your AI journey in <strong>{{stepCount}} steps</strong> by choosing your areas of interest and tailoring your experience to your job roles and domains."}, "jobRole": {"customRoleLabel": "Enter your job role", "customRolePlaceholder": "Type your specific job role or title", "noResults": "No job roles found matching your search. Try with different keywords.", "searchPlaceholder": "Type at least 3 characters to search job roles..."}, "industry": {"showAll": "Show all industries", "hide": "Hide industries"}, "password": {"newPassword": "New password", "confirmPassword": "Confirm password", "requirements": "Password strenght", "validation": {"minLength": "At least 8 characters", "hasUpperCase": "At least one uppercase letter", "hasLowerCase": "At least one lowercase letter", "hasSpecialChar": "At least one special character (!@#$%^&*(),.)", "passwordsMatch": "Passwords match"}}, "form": {"selectOption": "Select an option", "required": "This field is required", "acceptTerms": "Please accept the terms and conditions"}, "terms": {"accept": "I accept the", "privacyPolicy": "Privacy Policy", "and": "and", "termsAndConditions": "Terms and Conditions"}, "completion": {"processing": "Thank you for your time! We're setting up the platform for you.", "success": "Onboarding completed successfully!", "passwordTooltip": "Please enter your password according to the rules."}, "mandatoryVideo": {"title": "Mandatory Training Video", "description": "Please watch this mandatory training video before completing the onboarding process.", "watchRequirement": "MANDATORY: You must watch the entire video before proceeding. The video cannot be skipped, paused, or fast-forwarded. Onboarding will automatically complete when the video ends.", "completed": "Video completed! You can now proceed to complete your onboarding.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_Final_Welcome_to_the_AI_Business_School_180425_EN_V03.mp4/Video_Final_Welcome_to_the_AI_Business_School_180425_EN_V03.m3u8", "footerNote": "Please note that watching the video is mandatory to proceed. After completion, your information will be processed, and you'll be logged in automatically.", "skipVideo": "Skip this video"}}, "textUsecase": {"form": {"title": "Use case inputs", "tooltip": "Fill in the form fields to generate text", "addToFavorites": "Add to favorites", "poweredBy": "Powered by", "cancel": "Cancel", "generate": "Generate", "regenerate": "Regenerate"}, "output": {"title": "Output", "modifyResponse": "Modify response", "shorter": "Shorter", "longer": "Longer", "simpler": "Simpler", "copy": "Copy to clipboard", "copied": "Copied!", "clear": "Clear output", "emptyTitle": "Use case assistant", "emptyDescription": "You can start generating AI powered text by filling out the form on the left. The form contains {{count}} fields.", "feature1": "AI powered generation", "feature2": "Editing options", "feature3": "Easy copying", "feature4": "Quick clearing"}}, "imageUsecase": {"wait": {"process": "Please wait for the current process to complete...", "seconds": "Please wait a few seconds..."}, "error": {"savingProgress": "An error occurred while saving progress.", "prefix": "Error", "general": "An error occurred while generating the image"}, "form": {"title": "Image generator", "tooltip": "Fill in the fields to generate an image.", "aspectRatio": "Aspect Ratio", "aspectRatioTypes": {"square": "Square", "wide": "Wide", "portrait": "Portrait"}, "poweredBy": "Powered by", "model": "DALL-E 3", "cancel": "Cancel", "generateNew": "Generate New", "generate": "Generate"}, "output": {"title": "Generated Image", "emptyMessage": "The image you create will be shown here.", "generating": "Generating...", "newImage": "New", "imagesGenerated": "images generated", "download": "Download Image", "openInNewTab": "Open in New Tab", "clear": "Clear", "clickToZoom": "Click to zoom"}, "warning": {"maxImages": "You can create a maximum of 5 images."}}, "heygenVideoCreator": {"welcome": {"title": "HeyGen video creator", "description": "<p>Create professional videos in minutes! - e.g. for employee trainings, product updates, or stakeholder presentations. Engage your viewers with ease in different languages.</p><p>Customize every aspect to fit your goals—from selecting the ideal avatar and voice to efficiently crafting the perfect narrative for your videos, presentations, or proposals.</p>"}, "chooseAvatar": {"title": "Choose avatar", "female": "Female", "male": "Male", "addToFavorites": "Add to favorites"}, "chooseVoice": {"title": "Choose voice", "selectVoice": "Select a voice"}, "videoScript": {"title": "Video script", "generateVideo": "Generate Video", "generateVideoTooltip": "A maximum of 8 videos are allowed, uploaded videos are stored for a maximum of 7 days.", "generatedBy": "Powered by Hey<PERSON>en", "characterLimit": " / 500", "limitExceeded": "Text exceeds 500 character limit", "initialText": "Welcome to the world of AI-powered avatar video creation, tailored just for you. Here, you have the power to craft your story with customizable scripts, select from a range of voices, and even choose your preferred language. Unleash your creativity and dive into experimentation. You can change the avatar, language, and script using the tools on the left side of the page and watch your unique digital narrative come to life!", "noVoiceSelected": "Please select a voice before generating a video", "noAvatarSelected": "Please select an avatar before generating a video", "avatarNotFound": "Selected avatar could not be found. Please try selecting a different avatar.", "avatarIdNotFound": "Avatar ID could not be found. Please try selecting a different avatar.", "generationFailed": "Video generation failed. Please try again.", "noVideoIdInResponse": "No video ID in response. Please try again later.", "unexpectedError": "An unexpected error occurred. Please try again later."}, "previewVideo": {"title": "Preview video", "download": "Download", "generating": "Generating video", "generatingDescription": "Video generation is in progress and may take approximately 10 minutes. The exact time can vary depending on server traffic, script length, and other factors. Thank you for your patience!", "status": "Status", "videoGenerated": "Video generated successfully", "generationFailed": "Video generation failed. Please try again."}}, "playgrounds": {"common": {"backToPrompt": "Back to prompt", "typeMessage": "Type your message here...", "welcomeTitle": "How can I help you today?", "sendMessage": "Send message", "back": "Back"}, "chatGPT": {"title": "ChatGPT playground", "description": "Welcome to the world of ChatGPT, where words come alive and conversations flow like magic. Your pocket-sized genius is ready to assist. Watch our introductory video and dive in!", "tryItButton": "Try it yourself", "tryItYourself": "Try it yourself", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F28-11+%5BUPD%5D+__+%5BEN%5D-ChatGPT-Playground-Teaser-%5BPEREMECI%5D.mp4/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F28-11+%5BUPD%5D+__+%5BEN%5D-ChatGPT-Playground-Teaser-%5BPEREMECI%5D.m3u8", "settings": "Settings", "model": "Model", "temperature": "Temperature", "topP": "Top P", "frequencyPenalty": "Frequency penalty", "presencePenalty": "Presence penalty", "disclaimer": "Kindly refrain from sharing confidential business data, including customer, product, or employee information with ChatGPT during exercises to ensure the security of sensitive data at all times.", "welcome": "Welcome to ChatGPT Playground", "welcomeDescription": "Ask me anything and I'll do my best to assist you.", "showSettings": "Show settings", "settingsTooltip": "Here you can adjust the model, temperature, and other AI parameters", "samplePrompt": "What is generative AI, and how is it different from traditional AI?", "trainingProgress": {"currentStepTitle": "Introduction to ChatGPT"}}, "dallE": {"title": "DALL·E playground", "description": "With DALL·E, crafting one-of-a-kind visuals is as easy as a few keywords. Consider it your artistic muse, available at your fingertips. Watch our introductory video and dive in!", "tryItButton": "Try it yourself", "tryItYourself": "Try it yourself", "tryItDescription": "Give the prompt below a try or change the prompt text to try DALL·E now.", "typePrompt": "Describe the image you want to generate...", "generateImage": "Generate image", "disclaimer": "Please ensure that your image generation prompts comply with our content policy and do not request inappropriate or harmful content.", "samplePrompt": "A serene landscape painting of a mountain lake at sunset, with vibrant colors reflecting in the calm water", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F22-11+%5BUPD%5D+__+%5BEN%5D-Dall-E-Playground-Teaser-V03-%5BPEREMECI%5D.mp4/FINAL%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F22-11+%5BUPD%5D+__+%5BEN%5D-Dall-E-Playground-Teaser-V03-%5BPEREMECI%5D.m3u8", "trainingProgress": {"currentStepTitle": "Introduction to DALL-E"}}, "simpleAIAppCreator": {"title": "Simple AI App Creator playground", "description": "Quickly build custom AI applications with just a few clicks, powered by AI Business School", "createAppButton": "Create App", "myAppsTitle": "My created apps", "myAppsSubtitle": "Applications you have created.", "noAppsText": "You haven't created any apps yet.", "loadError": "An error occurred while loading your applications.", "noAppsYet": "You haven't created any applications yet. Let's get started!", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_+%5BEN%5D-Simple-AI-App-Creator-Teaser-24.10.2024_short+teaser_vf.mp4/Video_+%5BEN%5D-Simple-AI-App-Creator-Teaser-24.10.2024_short+teaser_vf.m3u8"}, "agenticWorkflowCreator": {"title": "Agentic Workflow Creator playground", "description": "Effortlessly build AI-powered agentic workflows using simple prompt flows.", "createAppButton": "Create Workflow", "myAppsTitle": "My created workflows", "myAppsSubtitle": "The applications you created.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_%5BEN%5D-Agentic-Workflow-Creator-Teaser-28.10.2024_vf.mp4/Video_%5BEN%5D-Agentic-Workflow-Creator-Teaser-28.10.2024_vf.m3u8", "loadError": "An error occurred while loading your workflows.", "noWorkflowsYet": "You don't have any workflows created yet."}, "heygen": {"title": "HeyGen Video Creator playground", "description": "Witness your ideas come to life on screen in minutes with <PERSON><PERSON><PERSON>. A skilled AI video producer at your disposal, always ready to craft videos with AI-generated spokespersons without requiring extra equipment. Watch our introductory video and dive in!", "tryItButton": "Try it now", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Vide0_12-12+%5BUPD%5D+_%5BEN%5D-Heygen-Playground-Teaser-V03-%5BPEREMECI%5D_vf.mp4/Vide0_12-12+%5BUPD%5D+_%5BEN%5D-Heygen-Playground-Teaser-V03-%5BPEREMECI%5D_vf.m3u8", "trainingProgress": {"currentStepTitle": "Introduction to DALL-E"}}, "advancedGenAI": {"title": "Advanced AI Creator playground", "description": "With this state-of-the-art tool from AI Planet, you can create powerful AI solutions with just a few commands.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Video_EN__GenAI-Teaser-optimized_24.10.2024_vf.mp4/Video_EN__GenAI-Teaser-optimized_24.10.2024_vf.m3u8", "trainingProgress": {"currentStepTitle": "Introduction to Advanced GenAI"}}, "chatGPTDeveloperTools": {"title": "ChatGPT IT Developer Tools", "description": "Further automate IT activities by leveraging GenAI", "tryItButton": "Try it yourself", "tryItYourself": "Try it yourself", "tryItDescription": "Use the sample prompt below or type your own prompt to try ChatGPT now.", "videoUrl": "https://d25yi7ujgmvrf2.cloudfront.net/Final%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F28-11+%5BNEW%5D+_+%5BEN%5D-IT-Playground-ChatGPT-Developers-Teaser-w+talking+head%5BPEREMECI%5D.mp4/Final%E2%9A%A0%EF%B8%8F%E2%9A%A0%EF%B8%8F28-11+%5BNEW%5D+_+%5BEN%5D-IT-Playground-ChatGPT-Developers-Teaser-w+talking+head%5BPEREMECI%5D.m3u8", "samplePrompt": "[language/framework/DBMS]: Python/Django/PostgreSQL\n[problem description]:  You are developing a ride-sharing application that connects drivers with passengers. Each driver and passenger has a specific location represented by latitude and longitude coordinates. Your task is to design an algorithm that efficiently matches drivers with nearby passengers, considering the distance between their locations. The application should find the best possible match to minimize wait times for passengers and optimize driver utilization.\n-----------------------------------------------------\nGenerate code with [language/framework/DBMS] to solve the problem: [problem description]. \n\nUse the instructions and suggestions below:\n\n- Security: Examine the code for potential vulnerabilities\n- Compatibility: Prepare suggestions about compatibility issues that may occur\n- Adding comments: Make the code more understandable by adding comment lines\n- Stay up to date: Notify about possible version differences\n\nIf you used any algorithm, explain it. Display [language/framework/DBMS] versions as a table.", "trainingProgress": {"currentStepTitle": "Introduction to ChatGPT Developer Tools"}}}, "assistantUsecase": {"pdfViewer": {"uploadTitle": "Upload a PDF file or use a sample document", "selectLocalFile": "Select local file", "or": "or", "loadSample": "Load sample document", "uploadFeatureTooltip": "This feature is not included in your current subscription – for more information, please contact your AI Business School account manager.", "prevPage": "Previous page", "nextPage": "Next page", "page": "Page", "of": "of", "zoomOut": "Zoom out", "zoomIn": "Zoom in", "fullscreen": "Fullscreen"}, "chat": {"title": "AI Assist", "askPlaceholder": "Ask me anything...", "sampleQuestions": "Sample questions:", "closeQuestions": "Close questions", "analyzingPdf": "Analyzing PDF, please wait...", "welcomeMessage": "Hello, click 'Load sample data' to view use case details.", "pdfError": "An error occurred while loading the PDF. Chat is still available.", "pdfMissing": "PDF URL is missing. Chat is still available.", "summarizeError": "An error occurred while summarizing the PDF. Please try again.", "pdfLoadWarning": "Please click the 'Load sample document' button to load a PDF document before continuing.", "pdfLoadSuccess": "PDF analyzed. How can I help you?", "messageError": "Sorry, an error occurred. Please try again.", "assistant": "Assistant", "you": "You"}}, "promptLibrary": {"noFavorites": "You don't have any favorite prompts yet. Browse the library and add some to your favorites!", "title": "Prompt Library", "description": "Explore and use our collection of effective prompts for various AI applications", "allPrompts": "All prompts", "yourFavoritePrompts": "Your favorite prompts", "selectProvider": "Select provider", "allProviders": "All providers", "selectApp": "Select app", "allApps": "All apps", "selectFunction": "Select function", "allFunctions": "All functions", "errorLoadingPrompts": "Error loading prompts. Please try again later.", "errorLoadingFavorites": "Error loading your favorite prompts. Please try again later.", "noPromptsFound": "No prompts found. Please try with a different filtering."}, "promptModal": {"title": "Try this prompt", "makeItYourOwn": "Make it your own", "author": "Provider", "worksIn": "Works in", "function": "Function", "copyPrompt": "Copy prompt", "copied": "Copied!", "tryThisPrompt": "Try this prompt", "tryItIn": "Try it in {{app}}", "open": "Open", "typeMessage": "Type your message here...", "howCanIHelp": "How can I help you today?", "settings": {"title": "Settings", "model": "Model", "temperature": "Temperature", "topP": "Top P", "frequencyPenalty": "Frequency penalty", "presencePenalty": "Presence penalty", "temperatureDescription": "Controls randomness: Lowering results in more focused and deterministic responses, while increasing leads to more diverse and creative outputs. (0 = focused, 2 = creative)", "topPDescription": "Controls diversity via nucleus sampling: Lower values keep responses more focused on highly probable tokens, while higher values allow for more diversity. (0 = focused, 1 = diverse)", "frequencyPenaltyDescription": "Reduces repetition by lowering the likelihood of using tokens that have already appeared in the text. Decreases the model's likelihood to repeat the same line verbatim. (0 = no penalty, 2 = high penalty)", "presencePenaltyDescription": "Encourages using new topics by penalizing tokens that appear in the whole text. Higher values encourage covering new topics. (0 = no penalty, 2 = high penalty)"}, "fileSelector": {"selectFile": "Select a file", "loadingFiles": "Loading files...", "pleaseSelectFile": "Please select a file to continue", "shared": "Shared"}, "warnings": {"loginTooltip": "For effective use, you must sign in with your Microsoft account."}, "backToPrompt": "Back to prompt", "loading": {"msalNotInitialized": "MSAL not initialized yet, initializing now...", "msalInitialized": "MSAL initialized in useEffect", "msalInitFailed": "MSAL initialization failed in useEffect", "msalInitDuringLogin": "MSAL initialized during login", "msalInitFailedDuringLogin": "MSAL initialization failed during login", "usingInteractiveMethod": "Silent token acquisition failed, using interactive method", "loginSuccess": "Successfully logged in with your Microsoft account: {{name}}", "loginError": "An error occurred during login. Please try again."}, "errors": {"tokenAcquisitionFailed": "Token acquisition failed", "personalFilesError": "Personal files could not be retrieved:", "sharedFilesError": "Shared files could not be retrieved:", "loginError": "Login error:"}, "logs": {"allUserFiles": "All user files:"}}, "shareYourIdea": {"title": "Share Your Idea", "description": "Share your own AI use case idea: envision a business scenario where AI can make a real difference in terms of optimizing existing processes or driving real business innovation.", "buttons": {"NewIdea": "Submit a new idea"}, "modal": {"title": "Share Your Idea", "submitting": "Submitting your idea..."}}, "ideation": {"form": {"title": "Share Your Idea", "defaultTitle": "Share Your Idea", "defaultDescription": "Share your idea for an AI use case", "formNotFound": "Form not found. Please try again later.", "errors": {"requiredFields": "Please fill in all required fields", "submission": "An error occurred while submitting your idea", "generic": "An unexpected error occurred. Please try again later."}, "success": {"submitted": "Your idea has been successfully submitted"}, "steps": {"describe": {"title": "Describe your idea", "label": "Description"}, "classify": {"title": "Classify your idea", "label": "Classification"}}}, "submissions": {"title": "My Submitted Ideas", "subtitle": "Ideas I have submitted", "noIdeas": "You haven't submitted any ideas yet"}}, "simpleAIApps": {"create": {"title": "Simple AI App Creator", "description": "Create your own use cases and simple AI apps.", "nameSection": {"title": "Name of the use case app", "placeholder": "What is the name of your app?"}, "descriptionSection": {"title": "Description of the use case app", "placeholder": "What is the description of your app?"}, "warning": "Kindly refrain from sharing confidential business data, including customer, product, or employee information with ChatGPT during exercises to ensure the security of sensitive data at all times.", "buttons": {"back": "Previous", "next": "Continue", "reviewApp": "Review app", "create": "Create"}, "tour": {"welcome": "Welcome to Simple AI App Creator", "next": "Next", "previous": "Previous", "done": "Done", "skip": "<PERSON><PERSON>", "showTourAgain": "Show Tour", "steps": {"appDescription": {"title": "App Name & Description", "content": "Start by giving your app a name and description. This will help users understand what your app does."}, "inputTypes": {"title": "Input Types", "content": "Choose from different input types to add to your form. You can add up to 5 form elements."}, "formElements": {"title": "Form Elements", "content": "Configure your form elements here. You can add names, default values, and options for dropdown fields."}, "navigationButtons": {"title": "Move to the next step", "content": "Use these buttons to navigate between steps or go back to edit your form."}}, "step2": {"welcome": "Welcome to Prompt Writing", "steps": {"promptEditor": {"title": "Prompt Editor", "content": "Write your prompt here. You can drag and drop form elements from the right panel to include them in your prompt."}, "editInputs": {"title": "Edit Inputs", "content": "This panel shows all your form elements. If you want to correct them you can click on the icon and go back."}, "appSettings": {"title": "Model Settings", "content": "Configure AI model settings like temperature, top-p, and frequency penalty to control the output behavior."}, "navigationButtons": {"title": "Review your app", "content": "Once you have completed the steps, you can preview your application and check how it works."}}}}, "loading": "Loading...", "review": {"title": "App review", "previous": "Previous", "creating": "Creating...", "updating": "Updating...", "createApp": "Create app", "updateApp": "Update app"}, "toast": {"success": {"title": "Application created successfully!", "description": "You will be redirected to the application page."}, "error": {"title": "An error occurred", "description": "An error occurred while creating the application. Please try again."}}, "formElements": {"title": "Form elements", "addField": "Add field", "inputName": "Input name", "inputNamePlaceholder": "Add input name", "defaultText": "Default text", "defaultTextPlaceholder": "Add default text (optional)", "addOptions": "Add options", "optionPlaceholder": "Option", "addOption": "Add Option"}, "inputTypes": {"title": "Input types", "shortText": "Short text", "paragraph": "Paragraph", "dropdown": "Dropdown", "radio": "Radio", "yesNo": "Yes / No", "checkbox": "Checkbox", "fileUpload": "File upload"}, "promptEditor": {"title": "Prompt writing", "elementsTitle": "Prompt elements", "elementsDescription": "You can include the input parameters you have created into your prompt by dragging and dropping them.", "yourPrompt": "Your prompt"}, "editInputs": {"title": "Edit Inputs"}, "appSettings": {"title": "Model settings", "model": "Model", "models": {"gpt4o": "GPT-4o"}, "temperature": {"title": "Temperature", "tooltip": "Lowering results in less random completions. As the temperature approaches zero, the model will become deterministic and repetitive."}, "topP": {"title": "Top P", "tooltip": "Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered."}, "frequencyPenalty": {"title": "Frequency penalty", "tooltip": "How much to penalize new tokens based on their existing frequency in the text so far. Decreases the model's likelihood to repeat the same line verbatim."}, "presencePenalty": {"title": "Presence penalty", "tooltip": "How much to penalize new tokens based on whether they appear in the text so far. Increases the model's likelihood to talk about new topics."}}}}, "useCases": {"title": {"default": "Use Cases", "withFunction": "{{function}} specific use cases"}, "description": "Discover carefully selected, relevant use cases for your business function and specific job area.", "filters": {"all": "All use cases", "text": "Text Generation", "video": "Video Generation", "image": "Image Generation"}, "noResults": "No use cases found for the selected filter."}, "sheets": {"it": {"title": "IT cheat sheet", "description": "Cheat sheets are condensed, informative documents that provide quick reference and guide you through a particular topic or skill. They are designed to help you grasp the key concepts, formulas, commands, or steps involved in a specific subject.", "reminder": "Remember, cheat sheets are meant to supplement your learning journey, not replace it entirely. They provide a handy resource to quickly access important information, but it's important to explore the topic in more depth for a comprehensive understanding. Happy learning!", "title1": "ChatGPT cheat sheet for data science", "title2": "Quality in IT projects cheat sheet", "title3": "Systems development life cycle cheat sheet", "title4": "Software design patterns and methodologies cheat sheet", "pdfUrl1": "https://docs.google.com/gview?url=https://aitrainer.aibusinessschool.com/resources/uploads/2023/07/saas-fa562cce-3952-428c-90ca-309625fa6648-ChatGPT_Cheat_Sheet_compressed.pdf&embedded=true", "pdfUrl2": "https://docs.google.com/gview?url=https://aitrainer.aibusinessschool.com/resources/uploads/2023/07/saas-decccfb8-fd60-4d4b-bdcf-99aa7d34d12f-quality-in-it-projects-3.pdf&embedded=true", "pdfUrl3": "https://docs.google.com/gview?url=https://aitrainer.aibusinessschool.com/resources/uploads/2023/07/saas-030102b8-6543-4af1-bdec-632e84e608c2-systems-development-life-cycle-3.pdf&embedded=true", "pdfUrl4": "https://docs.google.com/gview?url=https://aitrainer.aibusinessschool.com/resources/uploads/2023/07/saas-61d4b877-f07a-4abb-b6bc-0038eba22f58-sdm7306_software-design-patterns-and-methodology_1-2.pdf&embedded=true"}, "chatgpt": {"title": "ChatGPT cheat sheet", "description": "Cheat sheets are condensed, informative documents that provide quick reference and guide you through a particular topic or skill. They are designed to help you grasp the key concepts, formulas, commands, or steps involved in a specific subject.", "reminder": "Remember, cheat sheets are meant to supplement your learning journey, not replace it entirely. They provide a handy resource to quickly access important information, but it's important to explore the topic in more depth for a comprehensive understanding. Happy learning!", "pdfUrl": "https://aibs-content.s3.eu-central-2.amazonaws.com/CheatSheet_Six+best+prompting+practices+for+better+ChatGPT+results_EN_Vf.pdf"}, "dalle": {"title": "DALL·E Prompt sheet", "description": "Prompt sheets are condensed, informative documents that provide quick reference and guide you through a particular topic or skill. They are designed to help you grasp the key concepts, formulas, commands, or steps involved in a specific subject.", "reminder": "Remember, Prompt sheets are meant to supplement your learning journey, not replace it entirely. They provide a handy resource to quickly access important information, but it's important to explore the topic in more depth for a comprehensive understanding. Happy learning!", "pdfUrl": "https://aibscontentstorage.blob.core.windows.net/content-files/Common/Platform_PDFs/DALLE_Prompting_cheat_sheet_EN.pdf"}}, "videoPlayer": {"playbackSpeed": "Playback Speed", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen", "videoNotFound": "Video not found", "videoNotPlayable": "This video is currently not playable. Alternatively, you can watch the video on YouTube.", "watchOnYoutube": "Watch on YouTube", "videoError": "An error occurred while loading the video. Retrying...", "videoUnavailable": "Video content is currently unavailable."}, "support": {"form": {"errors": {"unavailable": "Support form not found. Please try again later.", "requiredFields": "Please fill in all required fields", "submission": "An error occurred while submitting your request", "submit": "An error occurred while submitting your request"}, "success": {"submitted": "Your request has been submitted successfully"}, "defaultTitle": "Support Request", "defaultDescription": "Fill out the form to submit a support request"}}, "course": {"nav": {"programDetails": "Program details", "outcomes": "Outcomes", "programContent": "Program content", "certificate": "Certificate"}, "actions": {"start": "Start", "continue": "Continue this course", "completed": "Completed", "tryAgain": "Try Again"}, "headers": {"skillsGain": "Skills you'll gain", "detailsToKnow": "Details to know", "instructors": "Instructors", "outcomes": "Outcomes", "programContent": "Program content"}, "complete": "Complete", "topics": "topics", "defaultTitle": "Course Name", "contentTypes": {"playground": "Playground", "videoGeneration": "Video generation", "generation": "{{type}} generation", "pdf": "PDF", "imageGeneration": "Image generation", "form": "Form", "text": "Text", "video": "Video"}, "navigation": {"next": "Next", "previous": "Previous", "completeCourse": "COMPLETE COURSE", "tooltips": {"watchVideo": "You must watch the video to complete this section.", "completeQuiz": "You must complete the quiz to proceed.", "interactContent": "You must interact with the content to proceed.", "completeAllTopics": "You must complete all topics", "passQuiz": "You must score at least 70% on the quiz to proceed", "completeAllContent": "You must complete all required content to proceed."}}, "details": {"duration": {"title": "Duration", "subtitle": "Overall {{duration}}, flexible, self-paced learning"}, "certificate": {"title": "Shareable certificate", "subtitle": "Receive a completion certificate, add it to LinkedIn"}, "aiPoints": {"title": "AI points", "subtitle": "Complete the course to earn 5 AI points"}}, "certificate": {"title": "Earn a shareable career certificate", "benefits": {"linkedIn": "Add this credential to your LinkedIn profile, resume, or CV", "socialMedia": "Share it on social media and in your performance review", "expertise": "Get recognized for your expertise in Generative AI"}, "certifiedBy": "Certificated by"}, "completionModal": {"title": "Congratulations!", "congratsTitle": "Congratulations!", "content": "Congratulations on completing the course! 🎉", "viewCertificate": "View your certificate", "downloadCertificate": "Download your certificate", "shareOnLinkedIn": "Share on LinkedIn", "continueJourney": "Continue journey", "backToHome": "Back to Home", "close": "Close"}, "defaultDescription": "This course offers an in-depth exploration of Generative AI and ChatGPT, focusing on its powerful techniques, tools, and applications. You'll gain practical experience through exercises with ChatGPT and DALL·E, while also understanding the limitations and future impact of GenAI. The course concludes with an assessment and certification, along with insights into AI adoption and productivity across various industries. This journey will equip you to harness the potential of GenAI in real-world scenarios.", "errors": {"loading": "An error occurred while loading course details. Please try again."}}, "courseContent": {"pdfPageOf": "Page {{pageNum}} / {{numPages}}", "usecaseSlugNotFound": "Usecase slug not found", "errorLoadingUsecase": "Error loading usecase:", "noUsecaseDataFor": "No usecase data available for", "playgroundNotSupported": "Playground type not supported", "videoUrlNotFound": "Video URL not found", "formSubmittedSuccess": "Your form has been submitted successfully.", "interactWithContent": "Please interact with the content before proceeding.", "watchVideoFirst": "Please watch at least 90% of the video.", "completeQuizFirst": "Please complete the quiz.", "progressSaving": "Your progress is being saved, please wait...", "contentCompleted": "Content completed!", "errorSavingProgress": "An error occurred while saving your progress!", "dataConflict": "Data update conflict occurred. Please wait a few seconds and try again.", "contentCompletedSuccess": "Your content is completed!", "topicCompleted": "Topic completed successfully!", "formSubmitted": "Form submitted successfully!", "quizPassed": "Quiz passed successfully!", "quizFailed": "You did not pass the quiz. Try again!", "usecaseCompleted": "Use case completed successfully!", "interactiveCompleted": "Interactive exercise completed successfully!", "videoCompleted": "Video completed successfully!", "pleaseWait": "Please wait while we update your progress...", "courseReadyToComplete": "Course ready to complete! Click Complete Course button.", "chapter": "Chapter", "topics": "topics", "pdf": {"errorLoading": "Error loading PDF", "openInNewTab": "Open in new tab", "notFound": "PDF not found", "download": "Download"}, "playground": {"completed": "ChatGPT Playground completed successfully!"}, "noOptionsFound": "No options found", "errorCompletingContent": "Error completing content!", "fileUpload": {"clickToUpload": "Click to upload files", "supportedFormats": "Supported formats: JPG, PNG, PDF, DOC, TXT, CSV", "maxSize": "Maximum file size: 10MB", "uploadedFiles": "Uploaded Files", "required": "File upload is required", "fileTooLarge": "File is too large", "fileTypeNotAllowed": "File type not allowed"}, "fileTooLarge": "File is too large", "fileTypeNotAllowed": "File type not allowed", "certificate": {"generated": "Certificate generated successfully!", "defaultTitle": "Course Certificate", "defaultInstructor": "Course Instructor", "congratulations": "Congratulations! 🎉", "completionMessage": "Congratulations on completing the course! 🎉 {{action}} your certificate, download it, or share your achievement on LinkedIn.", "view": "View", "get": "Get", "generating": "Generating...", "viewCertificate": "View Certificate", "generateCertificate": "Generate Certificate", "yourCertificate": "Your Certificate", "backToHome": "Back to Home", "share": "Share", "download": "Download", "shareOnLinkedIn": "Share on LinkedIn", "downloadCertificate": "Download Certificate", "certificatePreview": "Certificate Preview"}, "errors": {"userInfoMissing": "User information is missing. Please try again later.", "certificateGeneration": "Failed to generate certificate. Please try again."}}, "quiz": {"title": "Quiz", "notFound": "Quiz not found.", "enterYourAnswer": "Enter your answer", "correctAnswer": "Correct answer", "finish": "Finish Quiz", "tryAgain": "Try Again", "congratulationsPassed": "Congratulations! You passed the quiz", "sorryFailed": "Sorry, you didn't pass the quiz", "pleaseTryAgain": "Please try again.", "thankYouForSubmitting": "Thank you for submitting the quiz. Please review your responses before proceeding to the next step.", "dragItemsInstructions": "Drag items from the options below and drop them onto the corresponding drop zones.", "availableOptionsToMatch": "Available options to match:", "submittedResultsDisplayed": "Quiz submitted. Results are displayed below.", "allOptionsMatched": "All options have been matched. You can rearrange them if needed.", "matchItemsInstructions": "Match items with their correct descriptions:", "someMatchesIncorrect": "Some matches are incorrect or missing.", "allMatchesCorrect": "All matches are correct!", "unknownQuestionType": "Unknown question type", "correctMatch": "Correct match", "dropAnswerHere": "Drop answer here", "dragTip": "Tip: Drag options from the top and drop them into the matching drop zones next to each description.", "errors": {"failedToRetrieveData": "Failed to retrieve quiz data.", "errorLoading": "Error loading quiz", "errorOccurredWhileLoading": "An error occurred while loading the quiz:", "noValidDataFound": "No valid quiz data found.", "completionError": "An error occurred while completing the quiz. Please try again."}}, "otp": {"title": "Enter reset code", "subtitle": "We have sent a 6-digit code to {email}", "verification": "Verification code", "didntReceive": "Didn't receive the code?", "resendCode": "Resend code", "sending": "Sending...", "newPassword": "New password", "confirmPassword": "Confirm password", "resetPassword": "Reset password", "backToLogin": "Back to login", "passwordStrength": {"title": "Password Strength", "veryWeak": "Very Weak", "weak": "Weak", "medium": "Medium", "strong": "Strong", "requirements": {"lowercaseUppercase": "At least one lowercase & one uppercase letter", "number": "At least one number (0-9)", "specialChar": "At least one special character (!@#$%^&*.)", "minLength": "At least 8 characters"}}, "validation": {"required": "This field is required", "mustBeNumber": "Must be a number", "passwordRequired": "New password is required", "minLength": "Password must be at least 8 characters", "hasUpperCase": "Password must contain at least one uppercase letter", "hasLowerCase": "Password must contain at least one lowercase letter", "hasNumber": "Password must contain at least one number", "hasSpecialChar": "Password must contain at least one special character", "passwordsMatch": "Passwords do not match"}}}