import { useState } from 'react';
import {
  Con<PERSON>er,
  <PERSON><PERSON><PERSON>,
  Card,
  TextField,
  Button,
  Box,
  IconButton,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Divider,
  Stack,
  Switch,
  FormControlLabel,
  Paper,
} from '@mui/material';
import { useSettingsContext } from 'src/components/settings';
import Iconify from 'src/components/iconify';
import { useNavigate } from 'react-router-dom';
import { useForms } from 'src/apis';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import FieldTypeModal from '../components/FieldTypeModal';

const FIELD_TYPES = [
  { value: 'text', label: 'Text' },
  { value: 'email', label: 'Email' },
  { value: 'number', label: 'Number' },
  { value: 'textarea', label: 'Text Area' },
  { value: 'select', label: 'Select' },
  { value: 'radio', label: 'Radio' },
  { value: 'checkbox', label: 'Checkbox' },
  { value: 'date', label: 'Date' },
  { value: 'file', label: 'File' },
  { value: 'group', label: 'Group' },
  { value: 'repeater', label: 'Repeater' },
  { value: 'feedback', label: 'Feedback' },
];

const CONDITIONAL_OPERATORS = [
  { value: '==', label: 'Equals' },
  { value: '!=', label: 'Not Equals' },
  { value: '>', label: 'Greater Than' },
  { value: '<', label: 'Less Than' },
  { value: 'in', label: 'In' },
  { value: 'not_in', label: 'Not In' },
  { value: 'contains', label: 'Contains' },
  { value: 'starts_with', label: 'Starts With' },
  { value: 'ends_with', label: 'Ends With' },
];

const initialField = {
  name: '',
  label: '',
  type: 'text',
  required: false,
  description: '',
  validation: {
    min: '',
    max: '',
    pattern: '',
    message: '',
  },
  options: [],
  fields: [], // For group and repeater types
  conditional_logic: {
    enabled: false,
    rules: [[{ field: '', operator: 'has_any_value', value: '' }]], // Rule groups array
  },
  min: '', // For repeater
  max: '', // For repeater
  // File-specific properties
  file_settings: {
    max_size: 10, // MB
    allowed_types: ['image/*', 'application/pdf', '.doc', '.docx'], // MIME types and extensions
    multiple: false, // Allow multiple files
  },
};

const initialTopic = {
  title: '',
  description: '',
  fields: [],
};

const initialRule = {
  field: '',
  operator: '==',
  value: '',
};

export default function AddFormView() {
  const settings = useSettingsContext();
  const { addForm, error } = useForms();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    topics: [], // Form topics
    fields: [], // Geriye dönük uyumluluk için tutuyoruz
    status: 'draft',
  });

  const [isFieldModalOpen, setIsFieldModalOpen] = useState(false);
  const [currentTopicIndex, setCurrentTopicIndex] = useState(null);

  // Form başlık ve açıklama değişikliklerini işle
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Yeni topic ekle
  const handleAddTopic = () => {
    setFormData((prev) => ({
      ...prev,
      topics: [
        ...prev.topics,
        {
          ...initialTopic,
          title: `Topic ${prev.topics.length + 1}`,
          description: '', // Boş açıklama ile başlat
        },
      ],
    }));
  };

  // Topic başlığını değiştir
  const handleTopicTitleChange = (index, value) => {
    setFormData((prev) => {
      const updatedTopics = [...prev.topics];
      updatedTopics[index] = {
        ...updatedTopics[index],
        title: value,
      };
      return {
        ...prev,
        topics: updatedTopics,
      };
    });
  };

  // Topic açıklamasını değiştir
  const handleTopicDescriptionChange = (index, value) => {
    setFormData((prev) => {
      const updatedTopics = [...prev.topics];
      updatedTopics[index] = {
        ...updatedTopics[index],
        description: value,
      };
      return {
        ...prev,
        topics: updatedTopics,
      };
    });
  };

  // Topic sil
  const handleRemoveTopic = (index) => {
    setFormData((prev) => {
      const updatedTopics = [...prev.topics];
      updatedTopics.splice(index, 1);
      return {
        ...prev,
        topics: updatedTopics,
      };
    });
  };

  // Field ekleme modalını aç
  const handleAddField = (topicIndex) => {
    setCurrentTopicIndex(topicIndex);
    setIsFieldModalOpen(true);
  };

  // Field tipini seç
  const handleFieldSelect = (fieldType) => {
    setFormData((prev) => {
      const updatedTopics = [...prev.topics];

      // Yeni alan için temel özellikleri oluştur
      const newField = {
        ...initialField,
        type: fieldType.type,
        name: `field_${Date.now()}`, // Benzersiz isimler oluşturmak için timestamp kullan
        label: `${fieldType.label} ${updatedTopics[currentTopicIndex].fields.length + 1}`,
      };

      // Feedback alanı için özel validasyon ayarları
      if (fieldType.type === 'feedback') {
        newField.validation = {
          ...newField.validation,
          min: 0,
          max: 10,
        };
      }

      // Derin kopya oluşturuyoruz
      const updatedFields = [...updatedTopics[currentTopicIndex].fields];
      updatedFields.push(newField);

      updatedTopics[currentTopicIndex] = {
        ...updatedTopics[currentTopicIndex],
        fields: updatedFields,
      };

      console.log('Updated topic fields:', updatedTopics[currentTopicIndex].fields);

      return {
        ...prev,
        topics: updatedTopics,
      };
    });
    setIsFieldModalOpen(false);
  };

  // Field sil
  const handleRemoveField = (topicIndex, fieldIndex) => {
    setFormData((prev) => {
      const updatedTopics = [...prev.topics];
      updatedTopics[topicIndex].fields.splice(fieldIndex, 1);
      return {
        ...prev,
        topics: updatedTopics,
      };
    });
  };

  // Field değişikliklerini işle
  const handleFieldChange = (topicIndex, fieldIndex, field) => {
    setFormData((prev) => {
      const updatedTopics = [...prev.topics];
      updatedTopics[topicIndex].fields[fieldIndex] = field;
      return {
        ...prev,
        topics: updatedTopics,
      };
    });
  };

  const handleAddOption = (topicIndex, fieldIndex) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.map((topic, i) => {
        if (i === topicIndex) {
          return {
            ...topic,
            fields: topic.fields.map((field, j) => {
              if (j === fieldIndex) {
                return {
                  ...field,
                  options: [...(field.options || []), { label: '', value: '' }],
                };
              }
              return field;
            }),
          };
        }
        return topic;
      }),
    }));
  };

  const handleRemoveOption = (topicIndex, fieldIndex, optionIndex) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.map((topic, i) => {
        if (i === topicIndex) {
          return {
            ...topic,
            fields: topic.fields.map((field, j) => {
              if (j === fieldIndex) {
                return {
                  ...field,
                  options: field.options.filter((_, oi) => oi !== optionIndex),
                };
              }
              return field;
            }),
          };
        }
        return topic;
      }),
    }));
  };

  const handleOptionChange = (topicIndex, fieldIndex, optionIndex, key, value) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.map((topic, i) => {
        if (i === topicIndex) {
          return {
            ...topic,
            fields: topic.fields.map((field, j) => {
              if (j === fieldIndex) {
                return {
                  ...field,
                  options: field.options.map((opt, oi) => {
                    if (oi === optionIndex) {
                      return { ...opt, [key]: value };
                    }
                    return opt;
                  }),
                };
              }
              return field;
            }),
          };
        }
        return topic;
      }),
    }));
  };

  const handleValidationChange = (topicIndex, fieldIndex, key, value) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.map((topic, i) => {
        if (i === topicIndex) {
          return {
            ...topic,
            fields: topic.fields.map((field, j) => {
              if (j === fieldIndex) {
                // Feedback alanı için min ve max değerlerini koruyalım
                if (field.type === 'feedback' && (key === 'min' || key === 'max')) {
                  return field; // Değişiklik yapmadan mevcut alanı döndür
                }

                return {
                  ...field,
                  validation: {
                    ...field.validation,
                    [key]: value,
                  },
                };
              }
              return field;
            }),
          };
        }
        return topic;
      }),
    }));
  };

  const handleConditionalLogicChange = (topicIndex, fieldIndex, key, value) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.map((topic, i) => {
        if (i === topicIndex) {
          return {
            ...topic,
            fields: topic.fields.map((field, j) => {
              if (j === fieldIndex) {
                return {
                  ...field,
                  conditional_logic: {
                    ...field.conditional_logic,
                    [key]: value,
                  },
                };
              }
              return field;
            }),
          };
        }
        return topic;
      }),
    }));
  };

  const handleAddRuleGroup = (topicIndex, fieldIndex) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.map((topic, i) => {
        if (i === topicIndex) {
          return {
            ...topic,
            fields: topic.fields.map((field, j) => {
              if (j === fieldIndex) {
                return {
                  ...field,
                  conditional_logic: {
                    ...field.conditional_logic,
                    rules: [
                      ...field.conditional_logic.rules,
                      [{ field: '', operator: 'has_any_value', value: '' }],
                    ],
                  },
                };
              }
              return field;
            }),
          };
        }
        return topic;
      }),
    }));
  };

  const handleAddRule = (topicIndex, fieldIndex, groupIndex) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.map((topic, i) => {
        if (i === topicIndex) {
          return {
            ...topic,
            fields: topic.fields.map((field, j) => {
              if (j === fieldIndex) {
                const newRules = [...field.conditional_logic.rules];
                newRules[groupIndex] = [
                  ...newRules[groupIndex],
                  { field: '', operator: 'has_any_value', value: '' },
                ];
                return {
                  ...field,
                  conditional_logic: {
                    ...field.conditional_logic,
                    rules: newRules,
                  },
                };
              }
              return field;
            }),
          };
        }
        return topic;
      }),
    }));
  };

  const handleRemoveRule = (topicIndex, fieldIndex, groupIndex, ruleIndex) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.map((topic, i) => {
        if (i === topicIndex) {
          return {
            ...topic,
            fields: topic.fields.map((field, j) => {
              if (j === fieldIndex) {
                const newRules = [...field.conditional_logic.rules];
                if (newRules[groupIndex].length > 1) {
                  newRules[groupIndex] = newRules[groupIndex].filter((_, ri) => ri !== ruleIndex);
                } else {
                  newRules.splice(groupIndex, 1);
                }
                return {
                  ...field,
                  conditional_logic: {
                    ...field.conditional_logic,
                    rules: newRules,
                  },
                };
              }
              return field;
            }),
          };
        }
        return topic;
      }),
    }));
  };

  const handleRuleChange = (topicIndex, fieldIndex, groupIndex, ruleIndex, key, value) => {
    setFormData((prev) => ({
      ...prev,
      topics: prev.topics.map((topic, i) => {
        if (i === topicIndex) {
          return {
            ...topic,
            fields: topic.fields.map((field, j) => {
              if (j === fieldIndex) {
                const newRules = [...field.conditional_logic.rules];
                newRules[groupIndex] = newRules[groupIndex].map((rule, ri) => {
                  if (ri === ruleIndex) {
                    return { ...rule, [key]: value };
                  }
                  return rule;
                });
                return {
                  ...field,
                  conditional_logic: {
                    ...field.conditional_logic,
                    rules: newRules,
                  },
                };
              }
              return field;
            }),
          };
        }
        return topic;
      }),
    }));
  };

  const renderFieldOptions = (topicIndex, field, fieldIndex) => {
    if (!['select', 'radio', 'checkbox'].includes(field.type)) return null;

    return (
      <Box sx={{ ml: 1, mt: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Options
        </Typography>
        {field.options?.map((option, optionIndex) => (
          <Box key={optionIndex} sx={{ display: 'flex', gap: 1, mb: 1 }}>
            <TextField
              size="small"
              label="Label"
              value={option.label}
              onChange={(e) =>
                handleOptionChange(topicIndex, fieldIndex, optionIndex, 'label', e.target.value)
              }
            />
            <TextField
              size="small"
              label="Value"
              value={option.value}
              onChange={(e) =>
                handleOptionChange(topicIndex, fieldIndex, optionIndex, 'value', e.target.value)
              }
            />
            <IconButton
              size="small"
              color="error"
              onClick={() => handleRemoveOption(topicIndex, fieldIndex, optionIndex)}
            >
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Box>
        ))}
        <Button
          size="small"
          startIcon={<Iconify icon="solar:add-circle-bold" />}
          onClick={() => handleAddOption(topicIndex, fieldIndex)}
        >
          Add Option
        </Button>
      </Box>
    );
  };

  const renderValidation = (topicIndex, field, fieldIndex) => (
    <Box sx={{ ml: 2, mt: 2 }}>
      <Typography variant="subtitle2" sx={{ mb: 1 }}>
        Validation Rules
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <TextField
            size="small"
            label="Min Value/Length"
            value={field.validation.min}
            onChange={(e) => handleValidationChange(topicIndex, fieldIndex, 'min', e.target.value)}
            disabled={field.type === 'feedback'}
            helperText={field.type === 'feedback' ? 'Fixed at 0 for feedback' : ''}
          />
        </Grid>
        <Grid item xs={6}>
          <TextField
            size="small"
            label="Max Value/Length"
            value={field.validation.max}
            onChange={(e) => handleValidationChange(topicIndex, fieldIndex, 'max', e.target.value)}
            disabled={field.type === 'feedback'}
            helperText={field.type === 'feedback' ? 'Fixed at 10 for feedback' : ''}
          />
        </Grid>
        {field.type !== 'feedback' && (
          <Grid item xs={12}>
            <TextField
              fullWidth
              size="small"
              label="Pattern (Regex)"
              value={field.validation.pattern}
              onChange={(e) =>
                handleValidationChange(topicIndex, fieldIndex, 'pattern', e.target.value)
              }
            />
          </Grid>
        )}
      </Grid>
    </Box>
  );

  const renderConditionalLogic = (topicIndex, field, fieldIndex) => {
    const availableFields = formData.topics[topicIndex].fields
      .filter((f, i) => i !== fieldIndex)
      .map((f) => ({
        value: f.name,
        label: f.label || f.name,
      }));

    return (
      <Box sx={{ ml: 2, mt: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Conditional Logic
        </Typography>

        <FormControlLabel
          control={
            <Switch
              checked={field.conditional_logic.enabled}
              onChange={(e) =>
                handleConditionalLogicChange(topicIndex, fieldIndex, 'enabled', e.target.checked)
              }
            />
          }
          label="Conditional Logic"
        />

        {field.conditional_logic.enabled && (
          <>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                Show this field if
              </Typography>

              {field.conditional_logic.rules.map((ruleGroup, groupIndex) => (
                <Box key={groupIndex}>
                  {groupIndex > 0 && (
                    <Typography variant="body2" sx={{ my: 1 }}>
                      or
                    </Typography>
                  )}

                  {ruleGroup.map((rule, ruleIndex) => (
                    <Box
                      key={ruleIndex}
                      sx={{ display: 'flex', gap: 1, mb: 1, alignItems: 'center' }}
                    >
                      <FormControl size="small" sx={{ minWidth: 200 }}>
                        <Select
                          value={rule.field}
                          onChange={(e) =>
                            handleRuleChange(
                              topicIndex,
                              fieldIndex,
                              groupIndex,
                              ruleIndex,
                              'field',
                              e.target.value
                            )
                          }
                        >
                          {availableFields.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>

                      <FormControl size="small" sx={{ minWidth: 150 }}>
                        <Select
                          value={rule.operator}
                          onChange={(e) =>
                            handleRuleChange(
                              topicIndex,
                              fieldIndex,
                              groupIndex,
                              ruleIndex,
                              'operator',
                              e.target.value
                            )
                          }
                        >
                          <MenuItem value="has_any_value">Has any value</MenuItem>
                          <MenuItem value="is_empty">Is empty</MenuItem>
                          <MenuItem value="==">Equals</MenuItem>
                          <MenuItem value="!=">Does not equal</MenuItem>
                          <MenuItem value=">">Greater than</MenuItem>
                          <MenuItem value="<">Less than</MenuItem>
                          <MenuItem value="contains">Contains</MenuItem>
                        </Select>
                      </FormControl>

                      {!['has_any_value', 'is_empty'].includes(rule.operator) && (
                        <TextField
                          size="small"
                          value={rule.value}
                          onChange={(e) =>
                            handleRuleChange(
                              topicIndex,
                              fieldIndex,
                              groupIndex,
                              ruleIndex,
                              'value',
                              e.target.value
                            )
                          }
                        />
                      )}

                      {ruleIndex === ruleGroup.length - 1 && (
                        <Button
                          size="small"
                          onClick={() => handleAddRule(topicIndex, fieldIndex, groupIndex)}
                        >
                          and
                        </Button>
                      )}

                      <IconButton
                        size="small"
                        color="error"
                        onClick={() =>
                          handleRemoveRule(topicIndex, fieldIndex, groupIndex, ruleIndex)
                        }
                      >
                        <Iconify icon="solar:trash-bin-trash-bold" />
                      </IconButton>
                    </Box>
                  ))}
                </Box>
              ))}

              <Button
                size="small"
                variant="outlined"
                startIcon={<Iconify icon="solar:add-circle-bold" />}
                onClick={() => handleAddRuleGroup(topicIndex, fieldIndex)}
                sx={{ mt: 1 }}
              >
                Add rule group
              </Button>
            </Box>
          </>
        )}
      </Box>
    );
  };

  const renderRepeaterSettings = (topicIndex, field, fieldIndex) => {
    if (field.type !== 'repeater') return null;

    return (
      <Box sx={{ ml: 2, mt: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Repeater Settings
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <TextField
              size="small"
              type="number"
              label="Min Items"
              value={field.min}
              onChange={(e) =>
                handleFieldChange(topicIndex, fieldIndex, { ...field, min: e.target.value })
              }
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              size="small"
              type="number"
              label="Max Items"
              value={field.max}
              onChange={(e) =>
                handleFieldChange(topicIndex, fieldIndex, { ...field, max: e.target.value })
              }
            />
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Form gönder
  const handleSubmit = async () => {
    if (!formData.title) {
      toast.error('Form title is required');
      return;
    }

    if (formData.topics.length === 0) {
      toast.error('At least one topic is required');
      return;
    }

    for (const topic of formData.topics) {
      if (!topic.title) {
        toast.error('All topics must have a title');
        return;
      }
      if (topic.fields.length === 0) {
        toast.error(`Topic "${topic.title}" must have at least one field`);
        return;
      }
    }

    setLoading(true);
    const result = await addForm(formData);
    setLoading(false);

    if (result.success) {
      toast.success('Form created successfully');
      navigate('/cds/formBuilder/all');
    } else {
      toast.error(result.error || 'Failed to create form');
    }
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <ToastContainer />
      <Typography variant="h4" sx={{ mb: 3 }}>
        Create New Form
      </Typography>

      <Card sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Form Details
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Form Title"
              name="title"
              value={formData.title}
              onChange={handleFormChange}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Form Description"
              name="description"
              value={formData.description}
              onChange={handleFormChange}
              multiline
              rows={3}
            />
          </Grid>
        </Grid>
      </Card>

      {/* Form Topics */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">Form Topics</Typography>
          <Button
            variant="contained"
            startIcon={<Iconify icon="mdi:plus" />}
            onClick={handleAddTopic}
          >
            Add Topic
          </Button>
        </Box>

        {formData.topics.length === 0 ? (
          <Card sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              No topics added yet. Add a topic to start building your form.
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Iconify icon="mdi:plus" />}
              onClick={handleAddTopic}
            >
              Add First Topic
            </Button>
          </Card>
        ) : (
          formData.topics.map((topic, topicIndex) => (
            <Paper
              key={topicIndex}
              elevation={2}
              sx={{ p: 3, mb: 3, border: '1px solid', borderColor: 'divider' }}
            >
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 2,
                }}
              >
                <TextField
                  label="Topic Title"
                  value={topic.title}
                  onChange={(e) => handleTopicTitleChange(topicIndex, e.target.value)}
                  sx={{ flexGrow: 1, mr: 2 }}
                  required
                />
                <IconButton color="error" onClick={() => handleRemoveTopic(topicIndex)}>
                  <Iconify icon="mdi:delete" />
                </IconButton>
              </Box>

              <TextField
                fullWidth
                label="Topic Description"
                value={topic.description || ''}
                onChange={(e) => handleTopicDescriptionChange(topicIndex, e.target.value)}
                multiline
                rows={2}
                sx={{ mb: 2 }}
                placeholder="Enter a description for this topic (optional)"
                helperText="This description will be shown to users when filling the form"
              />

              <Divider sx={{ my: 2 }} />

              {/* Topic Fields */}
              <Box sx={{ mb: 2 }}>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 2,
                  }}
                >
                  <Typography variant="h6">Fields</Typography>
                  <Button
                    variant="outlined"
                    startIcon={<Iconify icon="mdi:plus" />}
                    onClick={() => handleAddField(topicIndex)}
                  >
                    Add Field
                  </Button>
                </Box>

                {topic.fields.length === 0 ? (
                  <Box
                    sx={{
                      textAlign: 'center',
                      p: 2,
                      bgcolor: 'background.neutral',
                      borderRadius: 1,
                    }}
                  >
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      No fields added to this topic yet.
                    </Typography>
                    <Button
                      size="small"
                      startIcon={<Iconify icon="mdi:plus" />}
                      onClick={() => handleAddField(topicIndex)}
                    >
                      Add Field
                    </Button>
                  </Box>
                ) : (
                  topic.fields.map((field, fieldIndex) => (
                    <Card key={fieldIndex} sx={{ p: 2, mb: 2, bgcolor: 'background.neutral' }}>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          mb: 2,
                        }}
                      >
                        <Typography variant="subtitle1">
                          {field.label} ({field.type})
                        </Typography>
                        <IconButton
                          color="error"
                          size="small"
                          onClick={() => handleRemoveField(topicIndex, fieldIndex)}
                        >
                          <Iconify icon="mdi:delete" />
                        </IconButton>
                      </Box>

                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Field Name"
                            value={field.name}
                            onChange={(e) => {
                              const updatedField = { ...field, name: e.target.value };
                              handleFieldChange(topicIndex, fieldIndex, updatedField);
                            }}
                            required
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Field Label"
                            value={field.label}
                            onChange={(e) => {
                              const updatedField = { ...field, label: e.target.value };
                              handleFieldChange(topicIndex, fieldIndex, updatedField);
                            }}
                            required
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Field Description"
                            value={field.description || ''}
                            onChange={(e) => {
                              const updatedField = { ...field, description: e.target.value };
                              handleFieldChange(topicIndex, fieldIndex, updatedField);
                            }}
                            multiline
                            rows={2}
                            placeholder="Enter a helpful description for this field (optional)"
                            helperText="This description will be shown to users as help text when filling the form"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControl fullWidth>
                            <InputLabel>Field Type</InputLabel>
                            <Select
                              value={field.type}
                              label="Field Type"
                              onChange={(e) => {
                                const updatedField = { ...field, type: e.target.value };
                                handleFieldChange(topicIndex, fieldIndex, updatedField);
                              }}
                            >
                              {FIELD_TYPES.map((type) => (
                                <MenuItem key={type.value} value={type.value}>
                                  {type.label}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={field.required}
                                onChange={(e) => {
                                  const updatedField = { ...field, required: e.target.checked };
                                  handleFieldChange(topicIndex, fieldIndex, updatedField);
                                }}
                              />
                            }
                            label="Required Field"
                          />
                        </Grid>
                      </Grid>

                      {/* Options for select, radio, checkbox */}
                      {['select', 'radio', 'checkbox'].includes(field.type) && (
                        <Box sx={{ mt: 2, mb: 2 }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>
                            Options
                          </Typography>
                          {field.options && field.options.length > 0 ? (
                            field.options.map((option, optionIndex) => (
                              <Box
                                key={optionIndex}
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  mb: 1,
                                  gap: 1,
                                }}
                              >
                                <TextField
                                  size="small"
                                  label="Label"
                                  value={option.label || ''}
                                  onChange={(e) =>
                                    handleOptionChange(
                                      topicIndex,
                                      fieldIndex,
                                      optionIndex,
                                      'label',
                                      e.target.value
                                    )
                                  }
                                  sx={{ flex: 1 }}
                                />
                                <TextField
                                  size="small"
                                  label="Value"
                                  value={option.value || ''}
                                  onChange={(e) =>
                                    handleOptionChange(
                                      topicIndex,
                                      fieldIndex,
                                      optionIndex,
                                      'value',
                                      e.target.value
                                    )
                                  }
                                  sx={{ flex: 1 }}
                                />
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() =>
                                    handleRemoveOption(topicIndex, fieldIndex, optionIndex)
                                  }
                                >
                                  <Iconify icon="mdi:delete" />
                                </IconButton>
                              </Box>
                            ))
                          ) : (
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              No options added yet.
                            </Typography>
                          )}
                          <Button
                            size="small"
                            startIcon={<Iconify icon="mdi:plus" />}
                            onClick={() => handleAddOption(topicIndex, fieldIndex)}
                          >
                            Add Option
                          </Button>
                        </Box>
                      )}

                      {/* Validation settings */}
                      <Box sx={{ mt: 2, mb: 2 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>
                          Validation
                        </Typography>
                        <Grid container spacing={2}>
                          {['text', 'number', 'email', 'textarea'].includes(field.type) && (
                            <>
                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  size="small"
                                  label="Min Value/Length"
                                  type="number"
                                  value={field.validation?.min || ''}
                                  onChange={(e) =>
                                    handleValidationChange(
                                      topicIndex,
                                      fieldIndex,
                                      'min',
                                      e.target.value
                                    )
                                  }
                                />
                              </Grid>
                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  size="small"
                                  label="Max Value/Length"
                                  type="number"
                                  value={field.validation?.max || ''}
                                  onChange={(e) =>
                                    handleValidationChange(
                                      topicIndex,
                                      fieldIndex,
                                      'max',
                                      e.target.value
                                    )
                                  }
                                />
                              </Grid>
                            </>
                          )}
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              size="small"
                              label="Pattern (Regex)"
                              value={field.validation?.pattern || ''}
                              onChange={(e) =>
                                handleValidationChange(
                                  topicIndex,
                                  fieldIndex,
                                  'pattern',
                                  e.target.value
                                )
                              }
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              size="small"
                              label="Error Message"
                              value={field.validation?.message || ''}
                              onChange={(e) =>
                                handleValidationChange(
                                  topicIndex,
                                  fieldIndex,
                                  'message',
                                  e.target.value
                                )
                              }
                            />
                          </Grid>
                        </Grid>
                      </Box>

                      {/* File settings */}
                      {field.type === 'file' && (
                        <Box sx={{ mt: 2, mb: 2 }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>
                            File Settings
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                size="small"
                                label="Max File Size (MB)"
                                type="number"
                                value={field.file_settings?.max_size || 10}
                                onChange={(e) => {
                                  const updatedField = {
                                    ...field,
                                    file_settings: {
                                      ...field.file_settings,
                                      max_size: Number(e.target.value),
                                    },
                                  };
                                  handleFieldChange(topicIndex, fieldIndex, updatedField);
                                }}
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={field.file_settings?.multiple || false}
                                    onChange={(e) => {
                                      const updatedField = {
                                        ...field,
                                        file_settings: {
                                          ...field.file_settings,
                                          multiple: e.target.checked,
                                        },
                                      };
                                      handleFieldChange(topicIndex, fieldIndex, updatedField);
                                    }}
                                  />
                                }
                                label="Allow Multiple Files"
                              />
                            </Grid>
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                size="small"
                                label="Allowed File Types"
                                value={field.file_settings?.allowed_types?.join(', ') || 'image/*, application/pdf, .doc, .docx'}
                                onChange={(e) => {
                                  const types = e.target.value.split(',').map(type => type.trim());
                                  const updatedField = {
                                    ...field,
                                    file_settings: {
                                      ...field.file_settings,
                                      allowed_types: types,
                                    },
                                  };
                                  handleFieldChange(topicIndex, fieldIndex, updatedField);
                                }}
                                helperText="Separate multiple types with commas (e.g., image/*, .pdf, .doc)"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      )}

                      {/* Conditional Logic */}
                      <Box sx={{ mt: 2 }}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={field.conditional_logic?.enabled || false}
                              onChange={(e) =>
                                handleConditionalLogicChange(
                                  topicIndex,
                                  fieldIndex,
                                  'enabled',
                                  e.target.checked
                                )
                              }
                            />
                          }
                          label="Enable Conditional Logic"
                        />

                        {field.conditional_logic?.enabled && (
                          <Box
                            sx={{ pl: 2, mt: 1, borderLeft: '1px solid', borderColor: 'divider' }}
                          >
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              Show this field if:
                            </Typography>

                            {field.conditional_logic.rules.map((ruleGroup, groupIndex) => (
                              <Box
                                key={groupIndex}
                                sx={{ mb: 2, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}
                              >
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                  sx={{ mb: 1, display: 'block' }}
                                >
                                  {groupIndex > 0 ? 'OR' : 'IF'}
                                </Typography>

                                {ruleGroup.map((rule, ruleIndex) => (
                                  <Box
                                    key={ruleIndex}
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      mb: 1,
                                      gap: 1,
                                    }}
                                  >
                                    {ruleIndex > 0 && (
                                      <Typography variant="caption" sx={{ minWidth: '30px' }}>
                                        AND
                                      </Typography>
                                    )}

                                    <FormControl size="small" sx={{ minWidth: '120px' }}>
                                      <InputLabel>Field</InputLabel>
                                      <Select
                                        value={rule.field || ''}
                                        label="Field"
                                        onChange={(e) =>
                                          handleRuleChange(
                                            topicIndex,
                                            fieldIndex,
                                            groupIndex,
                                            ruleIndex,
                                            'field',
                                            e.target.value
                                          )
                                        }
                                      >
                                        {topic.fields
                                          .filter((f, i) => i !== fieldIndex)
                                          .map((f, i) => (
                                            <MenuItem key={i} value={f.name}>
                                              {f.label}
                                            </MenuItem>
                                          ))}
                                      </Select>
                                    </FormControl>

                                    <FormControl size="small" sx={{ minWidth: '120px' }}>
                                      <InputLabel>Operator</InputLabel>
                                      <Select
                                        value={rule.operator || 'has_any_value'}
                                        label="Operator"
                                        onChange={(e) =>
                                          handleRuleChange(
                                            topicIndex,
                                            fieldIndex,
                                            groupIndex,
                                            ruleIndex,
                                            'operator',
                                            e.target.value
                                          )
                                        }
                                      >
                                        <MenuItem value="has_any_value">Has Any Value</MenuItem>
                                        <MenuItem value="is_empty">Is Empty</MenuItem>
                                        <MenuItem value="==">Equals</MenuItem>
                                        <MenuItem value="!=">Not Equals</MenuItem>
                                        <MenuItem value=">">Greater Than</MenuItem>
                                        <MenuItem value="<">Less Than</MenuItem>
                                        <MenuItem value="contains">Contains</MenuItem>
                                        <MenuItem value="starts_with">Starts With</MenuItem>
                                        <MenuItem value="ends_with">Ends With</MenuItem>
                                      </Select>
                                    </FormControl>

                                    {!['has_any_value', 'is_empty'].includes(rule.operator) && (
                                      <TextField
                                        size="small"
                                        label="Value"
                                        value={rule.value || ''}
                                        onChange={(e) =>
                                          handleRuleChange(
                                            topicIndex,
                                            fieldIndex,
                                            groupIndex,
                                            ruleIndex,
                                            'value',
                                            e.target.value
                                          )
                                        }
                                        sx={{ flex: 1 }}
                                      />
                                    )}

                                    <IconButton
                                      size="small"
                                      color="error"
                                      onClick={() =>
                                        handleRemoveRule(
                                          topicIndex,
                                          fieldIndex,
                                          groupIndex,
                                          ruleIndex
                                        )
                                      }
                                    >
                                      <Iconify icon="mdi:delete" />
                                    </IconButton>
                                  </Box>
                                ))}

                                <Button
                                  size="small"
                                  startIcon={<Iconify icon="mdi:plus" />}
                                  onClick={() => handleAddRule(topicIndex, fieldIndex, groupIndex)}
                                >
                                  Add AND Condition
                                </Button>
                              </Box>
                            ))}

                            <Button
                              size="small"
                              variant="outlined"
                              startIcon={<Iconify icon="mdi:plus" />}
                              onClick={() => handleAddRuleGroup(topicIndex, fieldIndex)}
                            >
                              Add OR Condition
                            </Button>
                          </Box>
                        )}
                      </Box>
                    </Card>
                  ))
                )}
              </Box>
            </Paper>
          ))
        )}
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mb: 5 }}>
        <Button variant="outlined" onClick={() => navigate('/cds/formBuilder/all')}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={loading}
          startIcon={loading ? <Iconify icon="mdi:loading" className="spin" /> : null}
        >
          Create Form
        </Button>
      </Box>

      <FieldTypeModal
        open={isFieldModalOpen}
        onClose={() => setIsFieldModalOpen(false)}
        onSelect={handleFieldSelect}
      />
    </Container>
  );
}
