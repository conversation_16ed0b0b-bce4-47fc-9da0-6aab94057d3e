import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>er,
  Typo<PERSON>,
  Box,
  Stack,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Divider,
  Chip,
  Paper,
  Grid,
  FormControlLabel,
  Switch,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useParams, useLocation } from 'react-router-dom';
import { useForms } from 'src/apis';
import { Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import Iconify from 'src/components/iconify';
import { IconButton } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import FieldTypeModal from '../components/FieldTypeModal';

// MongoDB benzeri ObjectID oluşturma fonksiyonu
const generateObjectId = () => {
  const timestamp = Math.floor(new Date().getTime() / 1000)
    .toString(16)
    .padStart(8, '0');
  const machineId = Math.floor(Math.random() * 16777216)
    .toString(16)
    .padStart(6, '0');
  const processId = Math.floor(Math.random() * 65536)
    .toString(16)
    .padStart(4, '0');
  const counter = Math.floor(Math.random() * 16777216)
    .toString(16)
    .padStart(6, '0');

  return timestamp + machineId + processId + counter;
};

const FIELD_TYPES = [
  { value: 'text', label: 'Text' },
  { value: 'email', label: 'Email' },
  { value: 'number', label: 'Number' },
  { value: 'textarea', label: 'Text Area' },
  { value: 'select', label: 'Select' },
  { value: 'radio', label: 'Radio' },
  { value: 'checkbox', label: 'Checkbox' },
  { value: 'date', label: 'Date' },
  { value: 'file', label: 'File' },
  { value: 'feedback', label: 'Feedback' },
];

const initialField = {
  name: '',
  label: '',
  type: 'text',
  required: false,
  description: '',
  validation: {
    min: '',
    max: '',
    pattern: '',
    message: '',
  },
  options: [],
  conditional_logic: {
    enabled: false,
    rules: [[{ field: '', operator: 'has_any_value', value: '' }]],
  },
  // File-specific properties
  file_settings: {
    max_size: 10, // MB
    allowed_types: ['image/*', 'application/pdf', '.doc', '.docx'], // MIME types and extensions
    multiple: false, // Allow multiple files
  },
};

function EditFormView() {
  const { t } = useTranslation();
  const { id } = useParams();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const language = searchParams.get('lang');
  const { getFormTranslation, updateFormTranslation, updateForm, getForms } = useForms();
  const [form, setForm] = useState(null);
  const [loading, setLoading] = useState(true);
  const [availableLanguages, setAvailableLanguages] = useState([]);
  const [isFieldModalOpen, setIsFieldModalOpen] = useState(false);
  const [currentTopicIndex, setCurrentTopicIndex] = useState(null);

  const languageMap = {
    en: 'English',
    es: 'Spanish',
    fr: 'French',
    de: 'German',
    it: 'Italian',
    pt: 'Portuguese',
    ru: 'Russian',
    'zh-Hans': 'Simplified Chinese',
    ja: 'Japanese',
    ko: 'Korean',
    ar: 'Arabic',
    tr: 'Turkish',
  };

  const getLanguageName = (code) => {
    return languageMap[code] || code.toUpperCase();
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Her zaman İngilizce (orijinal) form verisini al
        const enResponse = await getFormTranslation(id, 'en');

        // Mevcut çevirileri kontrol et
        if (enResponse.success) {
          // Mevcut çevirileri tespit et
          if (enResponse.data.translations) {
            setAvailableLanguages(Object.keys(enResponse.data.translations));
          }

          // Varsayılan olarak İngilizce formu set et
          const baseForm = enResponse.data;

          // Eğer bir dil seçilmişse ve İngilizce değilse, o dildeki çeviriyi al ve İngilizce form üzerine uygula
          if (language && language !== 'en') {
            const translationResponse = await getFormTranslation(id, language);
            if (translationResponse.success && translationResponse.data) {
              const translationData = translationResponse.data;

              // Temel form bilgilerini çeviriyle güncelle
              baseForm.title = translationData.title || baseForm.title;
              baseForm.description = translationData.description || baseForm.description;

              // Topicler ve fieldları çeviriyle güncelle
              if (baseForm.topics && translationData.topics) {
                baseForm.topics = baseForm.topics.map((topic, topicIndex) => {
                  const translatedTopic = translationData.topics[topicIndex];
                  if (!translatedTopic) return topic;

                  // Topic başlık ve açıklamasını güncelle
                  topic.title = translatedTopic.title || topic.title;
                  topic.description = translatedTopic.description || topic.description;

                  // Topic içindeki alanları güncelle
                  if (topic.fields && translatedTopic.fields) {
                    topic.fields = topic.fields.map((field, fieldIndex) => {
                      const translatedField = translatedTopic.fields[fieldIndex];
                      if (!translatedField) return field;

                      // Alan etiket ve açıklamasını güncelle
                      field.label = translatedField.label || field.label;
                      field.description = translatedField.description || field.description;

                      // Seçenekleri güncelle (varsa)
                      if (field.options && translatedField.options) {
                        field.options = field.options.map((option, optIndex) => {
                          const translatedOption = translatedField.options[optIndex];
                          if (!translatedOption) return option;

                          return {
                            ...option,
                            label: translatedOption.label || option.label,
                            // value değerini orijinalden koru
                          };
                        });
                      }

                      return field;
                    });
                  }

                  return topic;
                });
              }
            }
          }

          // Güncellenmiş formu ayarla
          setForm(baseForm);
        } else {
          toast.error('Form not found');
        }
      } catch (error) {
        console.error('Error fetching form:', error);
        toast.error('Form loading error');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, language, getFormTranslation]);

  const handleFormChange = (updatedForm) => {
    setForm(updatedForm);
  };

  const handleLanguageChange = (selectedLang) => {
    // Mevcut düzenleme sayfasına seçilen dil parametresiyle yönlendir
    window.location.href = `/cds/formBuilder/${id}/edit?lang=${selectedLang}`;
  };

  // Field ekleme modalını aç
  const handleAddField = (topicIndex) => {
    setCurrentTopicIndex(topicIndex);
    setIsFieldModalOpen(true);
  };

  // Field tipini seç
  const handleFieldSelect = (fieldType) => {
    setForm((prev) => {
      const updatedTopics = [...prev.topics];

      // Yeni alan için temel özellikleri oluştur
      const newField = {
        ...initialField,
        type: fieldType.type,
        name: `field_${Date.now()}`, // Benzersiz isimler oluşturmak için timestamp kullan
        label: `${fieldType.label} ${updatedTopics[currentTopicIndex].fields.length + 1}`,
      };

      // Feedback alanı için özel validasyon ayarları
      if (fieldType.type === 'feedback') {
        newField.validation = {
          ...newField.validation,
          min: 0,
          max: 10,
        };
      }

      // Eğer select, radio veya checkbox ise varsayılan seçenekler ekle
      if (['select', 'radio', 'checkbox'].includes(fieldType.type)) {
        newField.options = [
          { label: 'Option 1', value: '1' },
          { label: 'Option 2', value: '2' },
        ];
      }

      // Derin kopya oluşturuyoruz
      const updatedFields = [...updatedTopics[currentTopicIndex].fields];
      updatedFields.push(newField);

      updatedTopics[currentTopicIndex] = {
        ...updatedTopics[currentTopicIndex],
        fields: updatedFields,
      };

      return {
        ...prev,
        topics: updatedTopics,
      };
    });
    setIsFieldModalOpen(false);
  };

  // Field sil
  const handleRemoveField = (topicIndex, fieldIndex) => {
    setForm((prev) => {
      const updatedTopics = [...prev.topics];
      updatedTopics[topicIndex].fields.splice(fieldIndex, 1);
      return {
        ...prev,
        topics: updatedTopics,
      };
    });
  };

  // Seçenek ekle
  const handleAddOption = (topicIndex, fieldIndex) => {
    setForm((prev) => {
      const updatedTopics = [...prev.topics];
      const updatedFields = [...updatedTopics[topicIndex].fields];
      const field = updatedFields[fieldIndex];

      // Eğer options yoksa yeni bir array oluştur
      if (!field.options) {
        field.options = [];
      }

      // Yeni option ekle
      field.options.push({
        label: `Option ${field.options.length + 1}`,
        value: `${field.options.length + 1}`,
      });

      updatedFields[fieldIndex] = field;
      updatedTopics[topicIndex].fields = updatedFields;

      return {
        ...prev,
        topics: updatedTopics,
      };
    });
  };

  // Seçenek sil
  const handleRemoveOption = (topicIndex, fieldIndex, optionIndex) => {
    setForm((prev) => {
      const updatedTopics = [...prev.topics];
      const updatedFields = [...updatedTopics[topicIndex].fields];
      const field = { ...updatedFields[fieldIndex] };

      field.options = field.options.filter((_, idx) => idx !== optionIndex);

      updatedFields[fieldIndex] = field;
      updatedTopics[topicIndex].fields = updatedFields;

      return {
        ...prev,
        topics: updatedTopics,
      };
    });
  };

  const handleFormUpdate = async () => {
    try {
      if (language) {
        // Çeviri güncelleme
        const translationData = {
          title: form.title,
          description: form.description,
          status: form.status,
          topics: form.topics?.map((topic) => ({
            title: topic.title,
            description: topic.description,
            fields: topic.fields?.map((field) => ({
              _id: field._id || generateObjectId(),
              label: field.label,
              description: field.description,
              name: field.name,
              type: field.type,
              required: field.required,
              conditional_logic: field.conditional_logic,
              validation: field.validation,
              fields: field.fields,
              file_settings: field.file_settings,
              options: field.options?.map((option) => ({
                _id: option._id || generateObjectId(),
                label: option.label,
                value: option.value,
              })),
            })),
          })),
        };

        const result = await updateFormTranslation(id, language, translationData);
        if (result.success) {
          toast.success(`${getLanguageName(language)} translation updated successfully`);
        } else {
          toast.error(`Translation update error: ${result.error || 'Unknown error'}`);
        }
      } else {
        // İngilizce form güncelleme
        const result = await updateForm(id, form);
        if (result.success) {
          toast.success('Form updated successfully');
        } else {
          toast.error(`Form update error: ${result.error || 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error('Error updating form:', error);
      toast.error(`Error updating form: ${error.message || 'Unknown error'}`);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 5 }}>
          {t('lms.forms.edit')}
        </Typography>
        <Box
          sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}
        >
          <Typography>Loading...</Typography>
        </Box>
      </Container>
    );
  }

  if (!form) {
    return (
      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 5 }}>
          {t('lms.forms.edit')}
        </Typography>
        <Box
          sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}
        >
          <Typography>Form not found</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <ToastContainer position="top-right" autoClose={3000} />
      <Typography variant="h4" sx={{ mb: 5 }}>
        {t('lms.forms.edit')} {language ? `(${getLanguageName(language)})` : ''}
      </Typography>

      {/* Dil Seçimi */}
      {availableLanguages.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Language:
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            {availableLanguages.map((langCode) => (
              <Chip
                key={langCode}
                label={getLanguageName(langCode)}
                color={language === langCode ? 'primary' : 'default'}
                onClick={() => handleLanguageChange(langCode)}
                sx={{ mb: 1 }}
              />
            ))}
          </Stack>
        </Box>
      )}

      <Stack spacing={3}>
        <TextField
          fullWidth
          label="Form Title"
          value={form.title || ''}
          onChange={(e) => handleFormChange({ ...form, title: e.target.value })}
        />

        <TextField
          fullWidth
          multiline
          rows={3}
          label="Form Description"
          value={form.description || ''}
          onChange={(e) => handleFormChange({ ...form, description: e.target.value })}
        />

        <FormControl fullWidth>
          <InputLabel>Status</InputLabel>
          <Select
            value={form.status || 'draft'}
            label="Status"
            onChange={(e) => handleFormChange({ ...form, status: e.target.value })}
          >
            <MenuItem value="draft">Draft</MenuItem>
            <MenuItem value="active">Active</MenuItem>
            <MenuItem value="inactive">Inactive</MenuItem>
          </Select>
        </FormControl>

        <Divider sx={{ my: 2 }} />

        {/* Form Topics */}
        <Box sx={{ mt: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
            }}
          >
            <Typography variant="h6">Form Topics</Typography>
            <Button
              variant="outlined"
              startIcon={<Iconify icon="mdi:plus" />}
              onClick={() => {
                const newTopic = {
                  _id: `temp_${Date.now()}`,
                  title: `Topic ${form.topics ? form.topics.length + 1 : 1}`,
                  description: '',
                  fields: [],
                };
                handleFormChange({
                  ...form,
                  topics: [...(form.topics || []), newTopic],
                });
              }}
            >
              Add Topic
            </Button>
          </Box>

          {form.topics && form.topics.length > 0 ? (
            form.topics.map((topic, topicIndex) => (
              <Accordion key={topic._id || topicIndex}>
                <AccordionSummary
                  expandIcon={<Iconify icon="material-symbols:expand-more" />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                >
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      width: '100%',
                    }}
                  >
                    <Typography variant="subtitle1">{topic.title}</Typography>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleFormChange({
                          ...form,
                          topics: form.topics.filter((t, idx) => idx !== topicIndex),
                        });
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={2}>
                    <TextField
                      fullWidth
                      label="Topic Title"
                      value={topic.title || ''}
                      onChange={(e) => {
                        handleFormChange({
                          ...form,
                          topics: form.topics.map((t, idx) =>
                            idx === topicIndex ? { ...t, title: e.target.value } : t
                          ),
                        });
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Topic Description"
                      value={topic.description || ''}
                      onChange={(e) => {
                        handleFormChange({
                          ...form,
                          topics: form.topics.map((t, idx) =>
                            idx === topicIndex ? { ...t, description: e.target.value } : t
                          ),
                        });
                      }}
                      multiline
                      rows={2}
                    />

                    {/* Add Field Button at the top of fields */}
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                      <Button
                        variant="outlined"
                        startIcon={<Iconify icon="mdi:plus" />}
                        onClick={() => handleAddField(topicIndex)}
                      >
                        Add Field
                      </Button>
                    </Box>

                    {/* Alan Listesi */}
                    {topic.fields && topic.fields.length > 0 ? (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Fields:
                        </Typography>
                        {topic.fields.map((field, fieldIndex) => (
                          <Paper
                            key={field._id || fieldIndex}
                            sx={{
                              p: 2,
                              my: 1,
                              border: '1px solid',
                              borderColor: 'divider',
                              borderRadius: 1,
                              position: 'relative',
                            }}
                          >
                            <IconButton
                              size="small"
                              color="error"
                              sx={{ position: 'absolute', top: 8, right: 8 }}
                              onClick={() => handleRemoveField(topicIndex, fieldIndex)}
                            >
                              <Iconify icon="mdi:delete" />
                            </IconButton>

                            <Typography variant="subtitle2" gutterBottom>
                              {fieldIndex + 1}. {field.label || field.name}{' '}
                              <Chip
                                label={field.type}
                                size="small"
                                color="default"
                                sx={{ ml: 1 }}
                              />
                            </Typography>

                            <Grid container spacing={2}>
                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  label="Field Label"
                                  value={field.label || ''}
                                  onChange={(e) => {
                                    const updatedFields = [...topic.fields];
                                    updatedFields[fieldIndex] = {
                                      ...updatedFields[fieldIndex],
                                      label: e.target.value,
                                    };

                                    handleFormChange({
                                      ...form,
                                      topics: form.topics.map((t, idx) =>
                                        idx === topicIndex ? { ...t, fields: updatedFields } : t
                                      ),
                                    });
                                  }}
                                  sx={{ mb: 2 }}
                                />
                              </Grid>
                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  label="Field Description"
                                  value={field.description || ''}
                                  onChange={(e) => {
                                    const updatedFields = [...topic.fields];
                                    updatedFields[fieldIndex] = {
                                      ...updatedFields[fieldIndex],
                                      description: e.target.value,
                                    };

                                    handleFormChange({
                                      ...form,
                                      topics: form.topics.map((t, idx) =>
                                        idx === topicIndex ? { ...t, fields: updatedFields } : t
                                      ),
                                    });
                                  }}
                                  sx={{ mb: 2 }}
                                />
                              </Grid>
                            </Grid>

                            {/* Seçenekler */}
                            {['select', 'radio', 'checkbox'].includes(field.type) && (
                              <Box sx={{ mt: 2 }}>
                                <Box
                                  sx={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    mb: 1,
                                  }}
                                >
                                  <Typography variant="body2" gutterBottom>
                                    Options:
                                  </Typography>
                                  <Button
                                    size="small"
                                    startIcon={<Iconify icon="mdi:plus" />}
                                    onClick={() => handleAddOption(topicIndex, fieldIndex)}
                                  >
                                    Add Option
                                  </Button>
                                </Box>

                                {field.options &&
                                  field.options.map((option, optIndex) => (
                                    <Box
                                      key={option._id || optIndex}
                                      sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        mb: 1,
                                        gap: 1,
                                      }}
                                    >
                                      <TextField
                                        fullWidth
                                        size="small"
                                        label="Label"
                                        value={option.label || ''}
                                        onChange={(e) => {
                                          const updatedFields = [...topic.fields];
                                          const updatedOptions = [
                                            ...updatedFields[fieldIndex].options,
                                          ];
                                          updatedOptions[optIndex] = {
                                            ...updatedOptions[optIndex],
                                            label: e.target.value,
                                          };
                                          updatedFields[fieldIndex] = {
                                            ...updatedFields[fieldIndex],
                                            options: updatedOptions,
                                          };

                                          handleFormChange({
                                            ...form,
                                            topics: form.topics.map((t, idx) =>
                                              idx === topicIndex
                                                ? { ...t, fields: updatedFields }
                                                : t
                                            ),
                                          });
                                        }}
                                      />

                                      <TextField
                                        fullWidth
                                        size="small"
                                        label="Value"
                                        value={option.value || ''}
                                        onChange={(e) => {
                                          const updatedFields = [...topic.fields];
                                          const updatedOptions = [
                                            ...updatedFields[fieldIndex].options,
                                          ];
                                          updatedOptions[optIndex] = {
                                            ...updatedOptions[optIndex],
                                            value: e.target.value,
                                          };
                                          updatedFields[fieldIndex] = {
                                            ...updatedFields[fieldIndex],
                                            options: updatedOptions,
                                          };

                                          handleFormChange({
                                            ...form,
                                            topics: form.topics.map((t, idx) =>
                                              idx === topicIndex
                                                ? { ...t, fields: updatedFields }
                                                : t
                                            ),
                                          });
                                        }}
                                      />

                                      <IconButton
                                        size="small"
                                        color="error"
                                        onClick={() =>
                                          handleRemoveOption(topicIndex, fieldIndex, optIndex)
                                        }
                                      >
                                        <Iconify icon="mdi:delete" />
                                      </IconButton>
                                    </Box>
                                  ))}
                              </Box>
                            )}

                            {/* File Settings */}
                            {field.type === 'file' && (
                              <Box sx={{ mt: 2 }}>
                                <Typography variant="body2" gutterBottom>
                                  File Settings:
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      fullWidth
                                      size="small"
                                      label="Max File Size (MB)"
                                      type="number"
                                      value={field.file_settings?.max_size || 10}
                                      onChange={(e) => {
                                        const updatedFields = [...topic.fields];
                                        updatedFields[fieldIndex] = {
                                          ...updatedFields[fieldIndex],
                                          file_settings: {
                                            ...updatedFields[fieldIndex].file_settings,
                                            max_size: Number(e.target.value),
                                          },
                                        };

                                        handleFormChange({
                                          ...form,
                                          topics: form.topics.map((t, idx) =>
                                            idx === topicIndex ? { ...t, fields: updatedFields } : t
                                          ),
                                        });
                                      }}
                                    />
                                  </Grid>
                                  <Grid item xs={12} sm={6}>
                                    <FormControlLabel
                                      control={
                                        <Switch
                                          checked={field.file_settings?.multiple || false}
                                          onChange={(e) => {
                                            const updatedFields = [...topic.fields];
                                            updatedFields[fieldIndex] = {
                                              ...updatedFields[fieldIndex],
                                              file_settings: {
                                                ...updatedFields[fieldIndex].file_settings,
                                                multiple: e.target.checked,
                                              },
                                            };

                                            handleFormChange({
                                              ...form,
                                              topics: form.topics.map((t, idx) =>
                                                idx === topicIndex ? { ...t, fields: updatedFields } : t
                                              ),
                                            });
                                          }}
                                        />
                                      }
                                      label="Allow Multiple Files"
                                    />
                                  </Grid>
                                  <Grid item xs={12}>
                                    <TextField
                                      fullWidth
                                      size="small"
                                      label="Allowed File Types"
                                      value={field.file_settings?.allowed_types?.join(', ') || 'image/*, application/pdf, .doc, .docx'}
                                      onChange={(e) => {
                                        const types = e.target.value.split(',').map(type => type.trim());
                                        const updatedFields = [...topic.fields];
                                        updatedFields[fieldIndex] = {
                                          ...updatedFields[fieldIndex],
                                          file_settings: {
                                            ...updatedFields[fieldIndex].file_settings,
                                            allowed_types: types,
                                          },
                                        };

                                        handleFormChange({
                                          ...form,
                                          topics: form.topics.map((t, idx) =>
                                            idx === topicIndex ? { ...t, fields: updatedFields } : t
                                          ),
                                        });
                                      }}
                                      helperText="Separate multiple types with commas (e.g., image/*, .pdf, .doc)"
                                    />
                                  </Grid>
                                </Grid>
                              </Box>
                            )}
                          </Paper>
                        ))}
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          textAlign: 'center',
                          p: 2,
                          bgcolor: 'background.neutral',
                          borderRadius: 1,
                        }}
                      >
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          No fields added to this topic yet.
                        </Typography>
                        <Button
                          size="small"
                          startIcon={<Iconify icon="mdi:plus" />}
                          onClick={() => handleAddField(topicIndex)}
                        >
                          Add Field
                        </Button>
                      </Box>
                    )}
                  </Stack>
                </AccordionDetails>
              </Accordion>
            ))
          ) : (
            <Box
              sx={{
                textAlign: 'center',
                p: 3,
                bgcolor: 'background.neutral',
                borderRadius: 1,
              }}
            >
              <Typography variant="body1" sx={{ mb: 2 }}>
                No topics added yet. Please add a topic to edit the form.
              </Typography>
              <Button
                variant="outlined"
                startIcon={<Iconify icon="mdi:plus" />}
                onClick={() => {
                  const newTopic = {
                    _id: `temp_${Date.now()}`,
                    title: 'Topic 1',
                    description: '',
                    fields: [],
                  };
                  handleFormChange({
                    ...form,
                    topics: [newTopic],
                  });
                }}
              >
                Add First Topic
              </Button>
            </Box>
          )}
        </Box>

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 2,
            mt: 4,
          }}
        >
          <Button variant="outlined" onClick={() => window.history.back()}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleFormUpdate}>
            Save Changes
          </Button>
        </Box>
      </Stack>

      {/* Field Type Modal */}
      <FieldTypeModal
        open={isFieldModalOpen}
        onClose={() => setIsFieldModalOpen(false)}
        onSelect={handleFieldSelect}
      />
    </Container>
  );
}

export default EditFormView;
