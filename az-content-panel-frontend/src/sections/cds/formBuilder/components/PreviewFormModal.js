import {
  Dialog,
  DialogTitle,
  DialogContent,
  Button,
  TextField,
  MenuItem,
  Box,
  FormControl,
  FormLabel,
  FormControlLabel,
  Radio,
  RadioGroup,
  Checkbox,
  FormGroup,
  FormHelperText,
  Typography,
  Divider,
  Paper,
} from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';

export default function PreviewFormModal({ open, onClose, formData }) {
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm();

  const formValues = watch();

  // Geriye dönük uyumluluk için kontrol
  const formHasTopics = formData && Array.isArray(formData.topics) && formData.topics.length > 0;

  const evaluateConditionalLogic = (field) => {
    if (!field.conditional_logic?.enabled) return true;

    return field.conditional_logic.rules.some((ruleGroup) => {
      return ruleGroup.every((rule) => {
        const fieldValue = formValues[rule.field];

        switch (rule.operator) {
          case 'has_any_value':
            return fieldValue !== undefined && fieldValue !== '';
          case 'is_empty':
            return !fieldValue;
          case '==':
            return fieldValue == rule.value;
          case '!=':
            return fieldValue != rule.value;
          case '>':
            return Number(fieldValue) > Number(rule.value);
          case '<':
            return Number(fieldValue) < Number(rule.value);
          case 'in':
            // Virgülle ayrılmış değerler listesi içinde kontrol et
            if (!rule.value) return false;
            const valueList = rule.value.split(',').map((v) => v.trim());
            return valueList.includes(String(fieldValue));
          case 'not_in':
            // Virgülle ayrılmış değerler listesi içinde olmadığını kontrol et
            if (!rule.value) return false;
            const excludeList = rule.value.split(',').map((v) => v.trim());
            return !excludeList.includes(String(fieldValue));
          case 'contains':
            return String(fieldValue).includes(rule.value);
          case 'starts_with':
            return String(fieldValue).startsWith(rule.value);
          case 'ends_with':
            return String(fieldValue).endsWith(rule.value);
          default:
            return false;
        }
      });
    });
  };

  const onSubmit = (data) => {
    console.log('Form data:', data);
  };

  const renderField = (field) => {
    const shouldShow = evaluateConditionalLogic(field);
    if (!shouldShow) return null;

    const commonProps = {
      error: !!errors[field.name],
      helperText: errors[field.name]?.message,
      sx: { mb: 3 },
    };

    switch (field.type) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <Controller
            key={`field-${field._id}`}
            name={field.name}
            control={control}
            defaultValue=""
            rules={{
              required: field.required ? `${field.label} is required` : false,
              ...field.validation,
            }}
            render={({ field: { onChange, value } }) => (
              <TextField
                {...commonProps}
                fullWidth
                label={field.label}
                type={field.type}
                onChange={onChange}
                value={value}
                helperText={errors[field.name]?.message || field.description}
              />
            )}
          />
        );

      case 'textarea':
        return (
          <Controller
            key={`field-${field._id}`}
            name={field.name}
            control={control}
            defaultValue=""
            rules={{
              required: field.required ? `${field.label} is required` : false,
              ...field.validation,
            }}
            render={({ field: { onChange, value } }) => (
              <TextField
                {...commonProps}
                fullWidth
                label={field.label}
                multiline
                rows={4}
                onChange={onChange}
                value={value}
                helperText={errors[field.name]?.message || field.description}
              />
            )}
          />
        );

      case 'select':
        return (
          <Controller
            key={`field-${field._id}`}
            name={field.name}
            control={control}
            defaultValue=""
            rules={{
              required: field.required ? `${field.label} is required` : false,
            }}
            render={({ field: { onChange, value } }) => (
              <TextField
                {...commonProps}
                select
                fullWidth
                label={field.label}
                onChange={onChange}
                value={value}
                helperText={errors[field.name]?.message || field.description}
              >
                {field.options.map((option) => (
                  <MenuItem key={option._id} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            )}
          />
        );

      case 'radio':
        return (
          <Controller
            key={`field-${field._id}`}
            name={field.name}
            control={control}
            defaultValue=""
            rules={{
              required: field.required ? `${field.label} is required` : false,
            }}
            render={({ field: { onChange, value } }) => (
              <FormControl
                component="fieldset"
                error={!!errors[field.name]}
                sx={{ mb: 3, width: '100%' }}
              >
                <FormLabel component="legend">{field.label}</FormLabel>
                {errors[field.name] ? (
                  <FormHelperText sx={{ ml: -1, fontWeight: 'bold' }}>
                    {errors[field.name].message}
                  </FormHelperText>
                ) : field.description ? (
                  <FormHelperText sx={{ ml: -1, fontWeight: 'bold' }}>
                    {field.description}
                  </FormHelperText>
                ) : null}
                <RadioGroup value={value} onChange={onChange}>
                  {field.options.map((option) => (
                    <FormControlLabel
                      key={option._id}
                      value={option.value}
                      control={<Radio />}
                      label={option.label}
                    />
                  ))}
                </RadioGroup>
              </FormControl>
            )}
          />
        );

      case 'checkbox':
        return (
          <Controller
            key={`field-${field._id}`}
            name={field.name}
            control={control}
            defaultValue={[]}
            rules={{
              required: field.required ? `${field.label} is required` : false,
            }}
            render={({ field: { onChange, value = [] } }) => (
              <FormControl
                component="fieldset"
                error={!!errors[field.name]}
                sx={{ mb: 3, width: '100%' }}
              >
                <FormLabel component="legend">{field.label}</FormLabel>
                {errors[field.name] ? (
                  <FormHelperText>{errors[field.name].message}</FormHelperText>
                ) : field.description ? (
                  <FormHelperText>{field.description}</FormHelperText>
                ) : null}
                <FormGroup>
                  {field.options.map((option) => (
                    <FormControlLabel
                      key={option._id}
                      control={
                        <Checkbox
                          checked={value.includes(option.value)}
                          onChange={(e) => {
                            const newValue = e.target.checked
                              ? [...value, option.value]
                              : value.filter((v) => v !== option.value);
                            onChange(newValue);
                          }}
                        />
                      }
                      label={option.label}
                    />
                  ))}
                </FormGroup>
              </FormControl>
            )}
          />
        );

      case 'feedback':
        return (
          <Controller
            key={`field-${field._id}`}
            name={field.name}
            control={control}
            defaultValue=""
            rules={{
              required: field.required ? `${field.label} is required` : false,
              validate: (value) => {
                if (field.required && (value === '' || value === null || value === undefined)) {
                  return `${field.label} is required`;
                }
                if (value !== '' && (Number(value) < 0 || Number(value) > 10)) {
                  return `${field.label} must be between 0 and 10`;
                }
                return true;
              },
            }}
            render={({ field: { onChange, value } }) => (
              <FormControl
                component="fieldset"
                error={!!errors[field.name]}
                sx={{ mb: 3, width: '100%' }}
              >
                <FormLabel component="legend" sx={{ fontWeight: 'bold' }}>
                  {field.label}
                </FormLabel>
                {errors[field.name] ? (
                  <FormHelperText>{errors[field.name].message}</FormHelperText>
                ) : field.description ? (
                  <FormHelperText sx={{ ml: 0, fontWeight: 'bold' }}>
                    {field.description}
                  </FormHelperText>
                ) : null}
                <Box sx={{ mt: 2, mb: 1 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      maxWidth: 500,
                    }}
                  >
                    {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
                      <Button
                        key={rating}
                        variant={value === rating.toString() ? 'contained' : 'outlined'}
                        color={value === rating.toString() ? 'primary' : 'inherit'}
                        onClick={() => onChange(rating.toString())}
                        sx={{
                          minWidth: '40px',
                          height: '40px',
                          borderRadius: '4px',
                          mx: 0.5,
                        }}
                      >
                        {rating}
                      </Button>
                    ))}
                  </Box>
                </Box>
              </FormControl>
            )}
          />
        );

      case 'file':
        return (
          <Controller
            key={`field-${field._id}`}
            name={field.name}
            control={control}
            defaultValue={field.file_settings?.multiple ? [] : null}
            rules={{
              required: field.required ? `${field.label} is required` : false,
              validate: (files) => {
                if (field.required && (!files || (Array.isArray(files) && files.length === 0))) {
                  return `${field.label} is required`;
                }

                const fileList = Array.isArray(files) ? files : files ? [files] : [];
                const maxSize = (field.file_settings?.max_size || 10) * 1024 * 1024; // Convert MB to bytes
                const allowedTypes = field.file_settings?.allowed_types || ['image/*', 'application/pdf', '.doc', '.docx'];

                for (const file of fileList) {
                  if (file.size > maxSize) {
                    return `File "${file.name}" exceeds maximum size of ${field.file_settings?.max_size || 10}MB`;
                  }

                  const isValidType = allowedTypes.some(type => {
                    if (type.includes('*')) {
                      return file.type.startsWith(type.replace('*', ''));
                    }
                    if (type.startsWith('.')) {
                      return file.name.toLowerCase().endsWith(type.toLowerCase());
                    }
                    return file.type === type;
                  });

                  if (!isValidType) {
                    return `File "${file.name}" is not an allowed file type. Allowed types: ${allowedTypes.join(', ')}`;
                  }
                }

                return true;
              },
            }}
            render={({ field: { onChange, value } }) => (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold' }}>
                  {field.label}
                </Typography>
                {field.description && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {field.description}
                  </Typography>
                )}
                <input
                  type="file"
                  multiple={field.file_settings?.multiple || false}
                  accept={field.file_settings?.allowed_types?.join(',') || 'image/*,application/pdf,.doc,.docx'}
                  onChange={(e) => {
                    const files = Array.from(e.target.files);
                    if (field.file_settings?.multiple) {
                      onChange(files);
                    } else {
                      onChange(files[0] || null);
                    }
                  }}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: `1px solid ${errors[field.name] ? '#f44336' : '#c4c4c4'}`,
                    borderRadius: '4px',
                    backgroundColor: '#fafafa',
                    cursor: 'pointer',
                  }}
                />
                {errors[field.name] && (
                  <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
                    {errors[field.name].message}
                  </Typography>
                )}
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Max size: {field.file_settings?.max_size || 10}MB |
                  Allowed types: {field.file_settings?.allowed_types?.join(', ') || 'image/*, application/pdf, .doc, .docx'}
                  {field.file_settings?.multiple && ' | Multiple files allowed'}
                </Typography>
                {value && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Selected files:
                    </Typography>
                    {Array.isArray(value) ? (
                      value.map((file, index) => (
                        <Typography key={index} variant="body2" sx={{ ml: 1 }}>
                          • {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </Typography>
                      ))
                    ) : value ? (
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        • {value.name} ({(value.size / 1024 / 1024).toFixed(2)} MB)
                      </Typography>
                    ) : null}
                  </Box>
                )}
              </Box>
            )}
          />
        );

      default:
        return null;
    }
  };

  const renderTopicFields = (topic) => {
    return (
      <Paper key={topic._id} sx={{ p: 3, mb: 3, border: '1px solid', borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ mb: 1 }}>
          {topic.title}
        </Typography>
        {topic.description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {topic.description}
          </Typography>
        )}
        <Divider sx={{ mb: 3 }} />
        {topic.fields.map((field) => renderField(field))}
      </Paper>
    );
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Form Preview: {formData?.title}</DialogTitle>
      <DialogContent>
        {formData && (
          <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
            <Typography variant="body1" sx={{ mb: 3 }}>
              {formData.description}
            </Typography>

            {formHasTopics
              ? // Yeni yapı: Topic'ler ve içindeki field'lar
                formData.topics.map((topic) => renderTopicFields(topic))
              : // Eski yapı: Doğrudan field'lar
                formData.fields?.map((field) => renderField(field))}

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
              <Button variant="contained" type="submit">
                Submit
              </Button>
            </Box>
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
}
