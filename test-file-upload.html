<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100%;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .upload-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .file-list {
            margin: 10px 0;
        }
        .file-item {
            display: inline-block;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 5px 10px;
            margin: 5px;
        }
        .remove-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            margin-left: 5px;
            cursor: pointer;
        }
        .error {
            color: #dc3545;
            margin: 10px 0;
        }
        .success {
            color: #28a745;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>File Upload Test for Form Response</h1>
    
    <div class="form-container">
        <h2>Test File Upload</h2>
        
        <div>
            <label for="fileInput">Choose Files:</label>
            <input type="file" id="fileInput" class="file-input" multiple 
                   accept="image/*,application/pdf,.doc,.docx,.txt">
        </div>
        
        <button id="uploadBtn" class="upload-btn">Upload Files</button>
        
        <div id="uploadStatus"></div>
        
        <div id="fileList" class="file-list"></div>
    </div>

    <div class="form-container">
        <h2>Form Data Example</h2>
        <pre id="formData">{
    "formId": "6835795af53e3c9f57446fdd",
    "formType": "help",
    "title": "file form test",
    "description": "",
    "responses": [
        {
            "fieldId": "6835794df53e3c9f57446ce3",
            "name": "field_1748334905688",
            "type": "file",
            "value": null // Will be populated after upload
        }
    ]
}</pre>
    </div>

    <script>
        let uploadedFiles = [];
        
        document.getElementById('uploadBtn').addEventListener('click', async () => {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            const uploadBtn = document.getElementById('uploadBtn');
            const statusDiv = document.getElementById('uploadStatus');
            
            if (files.length === 0) {
                statusDiv.innerHTML = '<div class="error">Please select files to upload</div>';
                return;
            }
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = 'Uploading...';
            statusDiv.innerHTML = '<div>Uploading files...</div>';
            
            try {
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    
                    // File size check (50MB)
                    if (file.size > 50 * 1024 * 1024) {
                        throw new Error(`File ${file.name} is too large. Maximum size is 50MB.`);
                    }
                    
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    const response = await fetch('/api/form-responses/upload-file', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token') || 'your-token-here'}`,
                        },
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'File upload failed');
                    }
                    
                    const result = await response.json();
                    uploadedFiles.push(result.data);
                }
                
                statusDiv.innerHTML = '<div class="success">Files uploaded successfully!</div>';
                updateFileList();
                updateFormData();
                
            } catch (error) {
                console.error('File upload error:', error);
                statusDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'Upload Files';
            }
        });
        
        function updateFileList() {
            const fileListDiv = document.getElementById('fileList');
            fileListDiv.innerHTML = '';
            
            uploadedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    ${file.originalName} (${(file.size / 1024 / 1024).toFixed(2)} MB)
                    <button class="remove-btn" onclick="removeFile(${index})">×</button>
                `;
                fileListDiv.appendChild(fileItem);
            });
        }
        
        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            updateFileList();
            updateFormData();
        }
        
        function updateFormData() {
            const formDataPre = document.getElementById('formData');
            const formData = {
                "formId": "6835795af53e3c9f57446fdd",
                "formType": "help",
                "title": "file form test",
                "description": "",
                "responses": [
                    {
                        "fieldId": "6835794df53e3c9f57446ce3",
                        "name": "field_1748334905688",
                        "type": "file",
                        "value": uploadedFiles.length === 1 ? uploadedFiles[0] : uploadedFiles
                    }
                ]
            };
            
            formDataPre.textContent = JSON.stringify(formData, null, 2);
        }
    </script>
</body>
</html>
