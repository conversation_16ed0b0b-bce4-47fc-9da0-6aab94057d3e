const mongoose = require("mongoose");

const APIModelSchema = mongoose.Schema(
  {
    name: { type: String, required: true },
    apiKey: { type: String, required: true },
    apiUrl: { type: String, required: false },
    clientId: { type: String, required: false },
    tenantId: { type: String, required: false },
    secretId: { type: String, required: false },
    secretValue: { type: String, required: false },
    deploymentName: { type: String, required: false },
  },
  { versionKey: false }
);    

module.exports = mongoose.model("APIModel", APIModelSchema);
