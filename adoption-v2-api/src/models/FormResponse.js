const mongoose = require("mongoose");

const formResponseSchema = new mongoose.Schema(
  {
    formId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "Form",
    },
    formType: {
      type: String,
      required: true,
      enum: ["help", "ideation", "assessment", "feedback"],
    },
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    responses: [
      {
        fieldId: {
          type: mongoose.Schema.Types.ObjectId,
          required: true,
        },
        name: {
          type: String,
          required: true,
        },
        type: {
          type: String,
          required: true,
          enum: [
            "text",
            "number",
            "email",
            "textarea",
            "select",
            "radio",
            "checkbox",
            "date",
            "file",
          ],
        },
        value: {
          type: mongoose.Schema.Types.Mixed,
          required: true,
        },
      },
    ],
    status: {
      type: String,
      enum: ["draft", "submitted", "archived"],
      default: "draft",
    },
    submittedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Users",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("FormResponse", formResponseSchema);
