const APIModel = require("../models/APIModel");
const ENUM = require("../utils/enum");
const { httpResponse } = require("../utils/helpers");
const { AzureChatOpenAI } = require("@langchain/openai");
const { HumanMessage, SystemMessage } = require("@langchain/core/messages");

const NodeCache = require("node-cache");
const HeygenAvatars = require("../models/HeygenAvatars");
const APIRequestLimitModel = require("../models/APIRequestLimitModel");
const avatarCache = new NodeCache({ stdTTL: 3600 }); // 1 saat süreyle önbellekte tut

exports.addAPI = async (req, res) => {
  try {
    const { name, apiKey, apiSecret, apiUrl, clientId, tenantId, secretId, secretValue ,deploymentName } = req.body;
    const checkAPI = await APIModel.findOne({ name });
    if (checkAPI) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "API already exists",
        checkAPI.name
      );
    }
    const createdAPI = await APIModel.create({
      name,
      apiKey,
      apiSecret,
      apiUrl,
      clientId,
      tenantId,
      secretId,
      secretValue,
      deploymentName,
    });

    return httpResponse(
      res,
      ENUM.HTTP_CODES.CREATED,
      "success",
      "API added successfully",
      createdAPI
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "API adding failed",
      error
    );
  }
};

exports.getAPIs = async (req, res) => {
  try {
    const APIs = await APIModel.find();

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "APIs fetched successfully",
      APIs
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "APIs fetching failed",
      error
    );
  }
};

exports.getAPIById = async (req, res) => {
  const { _id } = req.params;

  try {
    const API = await APIModel.findById(_id);
    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "API fetched successfully",
      API
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "API fetching failed",
      error
    );
  }
};

exports.deleteAPI = async (req, res) => {
  const { _id } = req.params;
  try {
    await APIModel.findByIdAndDelete(_id);
    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "API deleted successfully",
      {}
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "API deleting failed",
      error
    );
  }
};

exports.editAPI = async (req, res) => {
  const { id } = req.params;
  const { name, apiKey, apiSecret, apiUrl, deploymentName } = req.body;
  try {
    const updatedAPI = await APIModel.findByIdAndUpdate(
      id,
      {
        name,
        apiKey,
        apiSecret,
        apiUrl,
        clientId,
        tenantId,
        secretId,
        secretValue,
        deploymentName,
      },
      { new: true }
    );
    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "API updated successfully",
      updatedAPI
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "API updating failed",
      error
    );
  }
};

exports.useAPI = async (req, res) => {
  try {
    const {
      _id,
      prompt,
      temperature,
      frequency,
      presence,
      assistantId,
      avatar_id,
      voice_id,
      instructions,
    } = req.body;

    const apiConfig = await APIModel.findById(_id);
    if (!apiConfig) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "API not found",
        null
      );
    }

    const deploymentName = apiConfig.apiUrl.split("/")[5];
    const instanceName = apiConfig.apiUrl.replace("https://", "").split(".")[0];
    const version = apiConfig.apiUrl.split("api-version=")[1];

    if (apiConfig.name.toLowerCase().includes("openai")) {
      const model = new AzureChatOpenAI({
        temperature: temperature,
        frequencyPenalty: frequency,
        presencePenalty: presence,
        azureOpenAIApiKey: apiConfig.apiKey,
        azureOpenAIApiInstanceName: instanceName,
        azureOpenAIApiDeploymentName: deploymentName,
        azureOpenAIApiVersion: version,
        streaming: true,
      });

      const messages = [
        new SystemMessage("You are a helpful AI assistant."),
        new HumanMessage(prompt.trim()),
      ];

      // Set headers for SSE
      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");

      let fullResponse = "";

      // Stream the response
      const stream = await model.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          fullResponse += chunk.content;
          res.write(`data: ${JSON.stringify({ content: chunk.content })}\n\n`);
        }
      }

      res.write(
        `data: ${JSON.stringify({ done: true, fullContent: fullResponse })}\n\n`
      );
      res.end();
    } else if (apiConfig.name.toLowerCase().includes("dall-e")) {
      const endpoint = `https://${instanceName}.openai.azure.com/openai/deployments/${deploymentName}/images/generations?api-version=${version}`;

      try {
        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "api-key": apiConfig.apiKey,
          },
          body: JSON.stringify({
            prompt: prompt.trim(),
            n: 1,
            size: "1024x1024",
            quality: "standard",
            style: "natural",
          }),
        });

        const responseData = await response.json();

        if (!response.ok) {
          return httpResponse(
            res,
            ENUM.HTTP_CODES.BAD_REQUEST,
            "error",
            responseData.error?.message || "Image generation failed",
            responseData.error || null
          );
        }

        return httpResponse(
          res,
          ENUM.HTTP_CODES.OK,
          "success",
          "Image generated successfully",
          { url: responseData.data[0].url }
        );
      } catch (error) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.INT_SERVER_ERROR,
          "error",
          "Image generation failed",
          { message: error.message }
        );
      }
    } else if (apiConfig.name.toLowerCase().includes("assistant")) {
      try {
        // Yeni bir sohbet oturumu başlat
        const threadResponse = await fetch(
          `https://${instanceName}.openai.azure.com/openai/threads?api-version=${version}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "api-key": apiConfig.apiKey,
            },
          }
        );

        const threadData = await threadResponse.json();
        const threadId = threadData.id;

        // Mesajı gönder
        const messageResponse = await fetch(
          `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/messages?api-version=${version}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "api-key": apiConfig.apiKey,
            },
            body: JSON.stringify({
              role: "user",
              content: prompt ? prompt.toString() : "",
            }),
          }
        );

        if (!messageResponse.ok) {
          const errorData = await messageResponse.json().catch(() => ({}));
          throw new Error(
            errorData.error?.message ||
              errorData.message ||
              `Message creation failed with status ${messageResponse.status}`
          );
        }

        const messageData = await messageResponse.json().catch(() => null);
        if (!messageData) {
          throw new Error("Invalid message response format");
        }

        // Çalıştırma başlat
        const runResponse = await fetch(
          `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/runs?api-version=${version}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "api-key": apiConfig.apiKey,
            },
            body: JSON.stringify({
              assistant_id: assistantId,
              instructions: instructions || undefined,
            }),
          }
        );

        const runData = await runResponse.json();
        let runStatus = "queued";
        let assistantMessage = null;

        // Çalıştırma durumunu kontrol et
        while (runStatus !== "completed" && runStatus !== "failed") {
          const statusResponse = await fetch(
            `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/runs/${runData.id}?api-version=${version}`,
            {
              headers: {
                "api-key": apiConfig.apiKey,
              },
            }
          );

          const statusData = await statusResponse.json();
          runStatus = statusData.status;

          if (runStatus === "completed") {
            // Mesajları al
            const messagesResponse = await fetch(
              `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/messages?api-version=${version}`,
              {
                headers: {
                  "api-key": apiConfig.apiKey,
                },
              }
            );

            const messagesData = await messagesResponse.json();
            assistantMessage = messagesData.data.find(
              (m) => m.role === "assistant"
            );
          }

          await new Promise((resolve) => setTimeout(resolve, 1000)); // 1 saniye bekle
        }

        if (assistantMessage) {
          return httpResponse(
            res,
            ENUM.HTTP_CODES.OK,
            "success",
            "Assistant response generated",
            {
              content: assistantMessage.content[0].text.value,
              threadId: threadId,
              runId: runData.id,
            }
          );
        } else {
          return httpResponse(
            res,
            ENUM.HTTP_CODES.INT_SERVER_ERROR,
            "error",
            "No assistant response received",
            null
          );
        }
      } catch (error) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.INT_SERVER_ERROR,
          "error",
          "Assistant API error",
          { message: error.message }
        );
      }
    } else if (apiConfig.name.toLowerCase().includes("heygen")) {
      try {
        const createResponse = await fetch(
          "https://api.heygen.com/v2/video/generate",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-Api-Key": apiConfig.apiKey,
            },
            body: JSON.stringify({
              caption: false,
              dimension: { width: 1280, height: 720 },
              video_inputs: [
                {
                  character: {
                    type: "avatar",
                    avatar_id: avatar_id,
                    scale: 1,
                    avatar_style: "normal",
                  },
                  background: {
                    type: "color",
                    value: "#f6f6fc",
                  },
                  voice: {
                    type: "text",
                    voice_id: voice_id,
                    input_text: prompt.trim(),
                    settings: {
                      stability: 0.5,
                      similarity: 0.75,
                      speed: 1.0,
                      pitch: 1.0,
                    },
                  },
                },
              ],
            }),
          }
        );

        if (!createResponse.ok) {
          const errorData = await createResponse.json();
          throw new Error(
            errorData.message || errorData.error || "Video generation failed"
          );
        }

        const responseData = await createResponse.json();

        if (!responseData.data?.video_id) {
          throw new Error("Video ID not found in response");
        }

        return httpResponse(
          res,
          ENUM.HTTP_CODES.OK,
          "success",
          "Video generation started",
          {
            video_id: responseData.data.video_id,
          }
        );
      } catch (error) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.INT_SERVER_ERROR,
          "error",
          "Video generation failed",
          {
            message: error.message,
            details: error.response?.data || error.stack,
          }
        );
      }
    } else {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "Unsupported API model",
        null
      );
    }
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An unexpected error occurred",
      { message: error.message }
    );
  }
};

exports.getHeygenVideo = async (req, res) => {
  const { video_id } = req.params;

  try {
    const getAPIKEY = await APIModel.findOne({ name: "heygen" }); // API key'i veritabanından al

    const response = await fetch(
      `https://api.heygen.com/v1/video_status.get?video_id=${video_id}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": getAPIKEY.apiKey,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch video status");
    }

    const data = await response.json();

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Video status fetched successfully",
      data
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Failed to fetch video status",
      { message: error.message }
    );
  }
};

exports.getHeygenAvatars = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    let allAvatars = avatarCache.get("heygen_avatars");

    if (!allAvatars) {
      const getAPIKEY = await APIModel.findOne({ name: "heygen" });
      const response = await fetch("https://api.heygen.com/v2/avatars", {
        method: "GET",
        headers: {
          accept: "application/json",
          "x-api-key": getAPIKEY.apiKey,
        },
      });

      const data = await response.json();
      const rawAvatars = data.data.avatars;

      // Avatarları ana isimlerine göre grupla
      const groupedAvatars = rawAvatars.reduce((acc, avatar) => {
        // Avatar adından ilk kelimeyi al (örn: "Abigail")
        const baseName = avatar.avatar_name.split(" ")[0];

        if (!acc[baseName]) {
          // Ana avatar objesi oluştur
          acc[baseName] = {
            avatar_id: avatar.avatar_id,
            avatar_name: baseName,
            gender: avatar.gender,
            preview_image_url: avatar.preview_image_url,
            preview_video_url: avatar.preview_video_url,
            variants: [],
          };
        } else if (avatar.avatar_name !== baseName) {
          // Eğer avatar adı ana isimden farklıysa variants'a ekle
          acc[baseName].variants.push({
            id: avatar.avatar_id,
            avatar_name: avatar.avatar_name,
            preview_image_url: avatar.preview_image_url,
            preview_video_url: avatar.preview_video_url,
          });
        }
        return acc;
      }, {});

      // Objeyi diziye çevir
      allAvatars = Object.values(groupedAvatars);

      // Önbelleğe kaydet
      avatarCache.set("heygen_avatars", allAvatars);
    }

    if (limit === -1) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.OK,
        "success",
        "Avatars fetched successfully",
        {
          avatars: allAvatars,
          pagination: {
            currentPage: 1,
            pageSize: allAvatars.length,
            totalItems: allAvatars.length,
            totalPages: 1,
          },
        }
      );
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedAvatars = allAvatars.slice(startIndex, endIndex);

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Avatars fetched successfully",
      {
        avatars: paginatedAvatars,
        pagination: {
          currentPage: page,
          pageSize: limit,
          totalItems: allAvatars.length,
          totalPages: Math.ceil(allAvatars.length / limit),
        },
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Failed to fetch avatars",
      error
    );
  }
};

exports.getHeygenAvatarsLocal = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const allAvatars = await HeygenAvatars.find({});

    // Avatarları ana isimlerine göre grupla
    const groupedAvatars = allAvatars.reduce((acc, avatar) => {
      // Avatar adından ilk kelimeyi al (örn: "Abigail")
      const baseName = avatar.avatar_name.split(" ")[0];

      if (!acc[baseName]) {
        // Ana avatar objesi oluştur
        acc[baseName] = {
          avatar_id: avatar.avatar_id,
          avatar_name: baseName,
          gender: avatar.gender,
          preview_image_url: avatar.preview_image_url,
          preview_video_url: avatar.preview_video_url,
          variants: [],
        };
      } else if (avatar.avatar_name !== baseName) {
        // Eğer avatar adı ana isimden farklıysa variants'a ekle
        acc[baseName].variants.push({
          id: avatar.avatar_id,
          avatar_name: avatar.avatar_name,
          preview_image_url: avatar.preview_image_url,
          preview_video_url: avatar.preview_video_url,
        });
      }
      return acc;
    }, {});

    // Objeyi diziye çevir
    const avatarsList = Object.values(groupedAvatars);

    if (limit === -1) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.OK,
        "success",
        "Avatars fetched successfully",
        {
          avatars: avatarsList,
          pagination: {
            currentPage: 1,
            pageSize: avatarsList.length,
            totalItems: avatarsList.length,
            totalPages: 1,
          },
        }
      );
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedAvatars = avatarsList.slice(startIndex, endIndex);

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Avatars fetched successfully",
      {
        avatars: paginatedAvatars,
        pagination: {
          currentPage: page,
          pageSize: limit,
          totalItems: avatarsList.length,
          totalPages: Math.ceil(avatarsList.length / limit),
        },
      }
    );
  } catch (error) {
    console.log(error.message);
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Failed to fetch avatars",
      error
    );
  }
};

exports.getHeygenVoices = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const getAPIKEY = await APIModel.findOne({ name: "heygen" });

    const response = await fetch("https://api.heygen.com/v2/voices", {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-api-key": getAPIKEY.apiKey,
      },
    });

    const data = await response.json();
    const allVoices = data.data.voices;

    if (limit === -1) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.OK,
        "success",
        "Voices fetched successfully",
        allVoices
      );
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedVoices = allVoices.slice(startIndex, endIndex);

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Voices fetched successfully",
      paginatedVoices
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Failed to fetch voices",
      error
    );
  }
};

exports.getAssistants = async (req, res) => {
  try {
    const apiConfig = await APIModel.findOne({ name: "assistant" });
    if (!apiConfig) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "API configuration not found",
        null
      );
    }

    const instanceName = apiConfig.apiUrl.replace("https://", "").split(".")[0];
    const version = apiConfig.apiUrl.split("api-version=")[1];

    const response = await fetch(
      `https://${instanceName}.openai.azure.com/openai/assistants?api-version=${version}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "api-key": apiConfig.apiKey,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch assistants");
    }
    const data = await response.json();

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Assistants fetched successfullyaaa",
      data
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Failed to fetch assistants",
      { message: error.message }
    );
  }
};

exports.textGeneration = async (req, res) => {
  try {
    const {
      name = "openai", // Varsayılan olarak openai
      prompt,
      temperature,
      frequency,
      presence,
      assistantId,
      instructions,
      assistant_settings,
      streaming = false, // Varsayılan olarak false
      stream, // Alternatif parametre adı
      html = false, // HTML çıktı için parametre
      formatHtml, // HTML çıktı için alternatif parametre
      htmlOutput, // HTML çıktı için alternatif parametre
    } = req.body;

    // Boolean değere dönüştürelim (stream veya streaming parametresini kabul et)
    const isStreaming =
      streaming === true ||
      streaming === "true" ||
      stream === true ||
      stream === "true";

    // HTML çıktı için boolean değer - birden fazla parametre adını kabul et
    const isHtml =
      html === true ||
      html === "true" ||
      formatHtml === true ||
      formatHtml === "true" ||
      htmlOutput === true ||
      htmlOutput === "true";

    // name değerini normalize et - tüm olası yazım şekillerini kabul et
    const normalizedName = name.toLowerCase().replace(/[-\s]/g, "");

    // API'yi bulmak için normalize edilmiş isimleri karşılaştır
    const apiConfig = await APIModel.findOne({
      $or: [
        { name: { $regex: new RegExp(`^${normalizedName}$`, "i") } },
        { name: { $regex: new RegExp(`^${name}$`, "i") } },
      ],
    });

    if (!apiConfig) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "API not found",
        null
      );
    }

    const deploymentName = apiConfig.apiUrl.split("/")[5];
    const instanceName = apiConfig.apiUrl.replace("https://", "").split(".")[0];
    const version = apiConfig.apiUrl.split("api-version=")[1];

    // Assistant API için kontrol
    if (apiConfig.name.toLowerCase().includes("assistant")) {
      try {
        // Yeni bir sohbet oturumu başlat
        const threadResponse = await fetch(
          `https://${instanceName}.openai.azure.com/openai/threads?api-version=${version}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "api-key": apiConfig.apiKey,
            },
          }
        );

        if (!threadResponse.ok) {
          throw new Error("Thread creation failed");
        }

        const threadData = await threadResponse.json();
        const threadId = threadData.id;

        // Mesajı gönder
        const messageResponse = await fetch(
          `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/messages?api-version=${version}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "api-key": apiConfig.apiKey,
            },
            body: JSON.stringify({
              role: "user",
              content: prompt || "",
            }),
          }
        );

        if (!messageResponse.ok) {
          throw new Error("Message creation failed");
        }

        // Run oluştur
        const runResponse = await fetch(
          `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/runs?api-version=${version}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "api-key": apiConfig.apiKey,
            },
            body: JSON.stringify({
              assistant_id: assistantId,
              instructions: instructions || undefined,
              metadata: assistant_settings
                ? {
                    assistant_name: assistant_settings.assistant_name,
                    assistant_type: assistant_settings.assistant_type,
                    file_name: assistant_settings.open_ai_assistant_file_name,
                    file_url: assistant_settings.open_ai_assistant_file_url,
                    file_id: assistant_settings.open_ai_file_id,
                    custom_system_prompt:
                      assistant_settings.custom_system_prompt,
                  }
                : undefined,
            }),
          }
        );

        if (!runResponse.ok) {
          throw new Error("Run creation failed");
        }

        const runData = await runResponse.json();

        // Run'ın tamamlanmasını bekle
        let isCompleted = false;
        let attempts = 0;
        const maxAttempts = 60; // 5 dakika (5s aralıklarla)
        let finalMessages = [];

        while (!isCompleted && attempts < maxAttempts) {
          const runStatusResponse = await fetch(
            `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/runs/${runData.id}?api-version=${version}`,
            {
              headers: {
                "api-key": apiConfig.apiKey,
              },
            }
          );

          if (!runStatusResponse.ok) {
            throw new Error("Failed to check run status");
          }

          const runStatus = await runStatusResponse.json();

          if (runStatus.status === "completed") {
            // Mesajları al
            const messagesResponse = await fetch(
              `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/messages?api-version=${version}`,
              {
                headers: {
                  "api-key": apiConfig.apiKey,
                },
              }
            );

            if (!messagesResponse.ok) {
              throw new Error("Failed to fetch messages");
            }

            const messagesData = await messagesResponse.json();
            finalMessages = messagesData.data;
            isCompleted = true;
          } else if (runStatus.status === "failed") {
            throw new Error(
              "Run failed: " +
                (runStatus.last_error?.message || "Unknown error")
            );
          }

          if (!isCompleted) {
            attempts++;
            await new Promise((resolve) => setTimeout(resolve, 5000)); // 5 saniye bekle
          }
        }

        if (!isCompleted) {
          throw new Error("Run timed out");
        }

        // Assistant'ın son mesajını bul
        const assistantMessage = finalMessages.find(
          (msg) => msg.role === "assistant"
        );

        if (!assistantMessage) {
          throw new Error("No assistant message found");
        }

        return httpResponse(
          res,
          ENUM.HTTP_CODES.OK,
          "success",
          "Response generated successfully",
          { content: assistantMessage.content[0].text.value }
        );
      } catch (error) {
        return httpResponse(
          res,
          ENUM.HTTP_CODES.INT_SERVER_ERROR,
          "error",
          "Assistant API error",
          { message: error.message }
        );
      }
    } else {
      // Normal text generation için mevcut kod
      const model = new AzureChatOpenAI({
        temperature: temperature,
        frequencyPenalty: frequency,
        presencePenalty: presence,
        azureOpenAIApiKey: apiConfig.apiKey,
        azureOpenAIApiInstanceName: instanceName,
        azureOpenAIApiDeploymentName: deploymentName,
        azureOpenAIApiVersion: version,
        streaming: isStreaming, // Boolean değeri kullan
      });

      // Sistem mesajı - HTML istenirse HTML formatında yanıt ver
      let systemPrompt;

      if (isHtml) {
        systemPrompt = `You are a helpful AI assistant that provides well-formatted HTML responses.

RESPONSE FORMAT INSTRUCTIONS:
1. Always provide your response as valid HTML content.
2. Use proper semantic HTML5 tags for structure:
   - <h1>, <h2>, <h3> for headings
   - <p> for paragraphs
   - <ul>/<ol> with <li> for lists
   - <table>, <tr>, <th>, <td> for tabular data
   - <blockquote> for quotes
   - <code> for code snippets
   - <strong> for bold text
   - <em> for italic text
   - <br> for line breaks
   - <a href="..."> for links when appropriate

3. Use CSS classes that can be styled later:
   - Add class="section" to main content sections
   - Add class="important" to highlight important information
   - Add class="note" for additional notes or tips
   - Add class="code-block" around <code> elements with multi-line code

4. DO NOT include <html>, <head>, <body> tags or DOCTYPE - focus only on content formatting.
5. Make sure all HTML tags are properly closed and nested.
6. Format content to be visually appealing and well-structured when rendered.

Remember that your HTML will be directly inserted into a webpage, so ensure it is valid and properly formatted.`;
      } else {
        systemPrompt = "You are a helpful AI assistant.";
      }

      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(prompt.trim()),
      ];

      // Streaming modunun kontrolü
      if (isStreaming) {
        // Streaming modu aktif ise event-stream olarak yanıt gönder
        res.writeHead(200, {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
          "X-Accel-Buffering": "no", // NGINX için önemli
        });

        try {
          let fullResponse = "";

          // Stream başladığına dair bildirim
          res.write(
            `data: ${JSON.stringify({
              status: "started",
              html: isHtml,
              streaming: true,
            })}\n\n`
          );

          const stream = await model.stream(messages);

          for await (const chunk of stream) {
            if (chunk.content) {
              fullResponse += chunk.content;

              const chunkData = JSON.stringify({
                content: chunk.content,
                html: isHtml,
                streaming: true,
              });
              res.write(`data: ${chunkData}\n\n`);
              // Flush yaparak hemen gönderilmesini sağla
              if (res.flush) res.flush();
            }
          }

          // Tamamlanma bildirimi gönder
          res.write(
            `data: ${JSON.stringify({
              done: true,
              fullContent: fullResponse,
              html: isHtml,
              streaming: true,
            })}\n\n`
          );
          res.end();
        } catch (streamError) {
          return httpResponse(
            res,
            ENUM.HTTP_CODES.INT_SERVER_ERROR,
            "error",
            "Streaming error",
            { message: streamError.message }
          );
        }
      } else {
        // Streaming modu kapalı ise normal bir HTTP yanıtı gönder
        const response = await model.invoke(messages);
        return httpResponse(res, ENUM.HTTP_CODES.OK, "success", "AI response", {
          content: response.content,
          html: isHtml,
          streaming: false,
        });
      }
    }
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Text generation failed",
      { message: error.message }
    );
  }
};

exports.imageGeneration = async (req, res) => {
  try {
    const { name = "dall-e", prompt, aspectRatio = "1:1" } = req.body;

    const apiConfig = await APIModel.findOne({ name: name.toLowerCase() });
    if (!apiConfig) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "API not found",
        null
      );
    }

    const deploymentName = apiConfig.apiUrl.split("/")[5];
    const instanceName = apiConfig.apiUrl.replace("https://", "").split(".")[0];
    const version = apiConfig.apiUrl.split("api-version=")[1];
    const endpoint = `https://${instanceName}.openai.azure.com/openai/deployments/${deploymentName}/images/generations?api-version=${version}`;

    // Aspect ratio'ya göre boyut belirleme
    let size;
    switch (aspectRatio) {
      case "16:9":
        size = "1792x1024";
        break;
      case "9:16":
        size = "1024x1792";
        break;
      default:
        size = "1024x1024"; // 1:1 için varsayılan
    }

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": apiConfig.apiKey,
      },
      body: JSON.stringify({
        prompt: prompt.trim(),
        n: 1,
        size: size,
        quality: "standard",
        style: "natural",
      }),
    });

    const responseData = await response.json();

    if (!response.ok) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        responseData.error?.message || "Image generation failed",
        responseData.error || null
      );
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Image generated successfully",
      { url: responseData.data[0].url }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Image generation failed",
      { message: error.message }
    );
  }
};

exports.videoGeneration = async (req, res) => {
  try {
    const { name = "heygen", prompt, avatar_id, voice_id } = req.body;

    const apiConfig = await APIModel.findOne({ name: name.toLowerCase() });
    if (!apiConfig) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "API not found",
        null
      );
    }

    const createResponse = await fetch(
      "https://api.heygen.com/v2/video/generate",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": apiConfig.apiKey,
        },
        body: JSON.stringify({
          caption: false,
          dimension: { width: 1280, height: 720 },
          video_inputs: [
            {
              character: {
                type: "avatar",
                avatar_id: avatar_id,
                scale: 1,
                avatar_style: "normal",
              },
              background: {
                type: "color",
                value: "#f6f6fc",
              },
              voice: {
                type: "text",
                voice_id: voice_id,
                input_text: prompt.trim(),
                settings: {
                  stability: 0.5,
                  similarity: 0.75,
                  speed: 1.0,
                  pitch: 1.0,
                },
              },
            },
          ],
        }),
      }
    );

    const responseData = await createResponse.json();

    if (!createResponse.ok) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        responseData.message || "Video generation failed",
        responseData
      );
    }

    if (!responseData.data?.video_id) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.BAD_REQUEST,
        "error",
        "Video ID not found in response",
        responseData
      );
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Video generation started",
      {
        video_id: responseData.data.video_id,
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Video generation failed",
      {
        message: error.message || "An unexpected error occurred",
        error: error,
      }
    );
  }
};

exports.assistant = async (req, res) => {
  try {
    const { _id, prompt, assistantId, instructions } = req.body;
    const apiConfig = await APIModel.findById(_id);
    if (!apiConfig) {
      return httpResponse(
        res,
        ENUM.HTTP_CODES.NOT_FOUND,
        "error",
        "API not found",
        null
      );
    }
    const instanceName = apiConfig.apiUrl.replace("https://", "").split(".")[0];
    const version = apiConfig.apiUrl.split("api-version=")[1];

    // Önce bir thread oluştur
    const threadResponse = await fetch(
      `https://${instanceName}.openai.azure.com/openai/threads?api-version=${version}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "api-key": apiConfig.apiKey,
        },
      }
    );

    if (!threadResponse.ok) {
      throw new Error("Thread creation failed");
    }

    const threadData = await threadResponse.json();
    const threadId = threadData.id;

    // Mesajı gönder
    const messageResponse = await fetch(
      `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/messages?api-version=${version}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "api-key": apiConfig.apiKey,
        },
        body: JSON.stringify({
          role: "user",
          content: prompt.trim(),
        }),
      }
    );

    if (!messageResponse.ok) {
      throw new Error("Message creation failed");
    }

    // Run oluştur
    const runResponse = await fetch(
      `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/runs?api-version=${version}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "api-key": apiConfig.apiKey,
        },
        body: JSON.stringify({
          assistant_id: assistantId,
          instructions: instructions || undefined,
        }),
      }
    );

    if (!runResponse.ok) {
      throw new Error("Run creation failed");
    }

    const runData = await runResponse.json();

    // Run'ın tamamlanmasını bekle
    let isCompleted = false;
    let attempts = 0;
    const maxAttempts = 60; // 5 dakika (5s aralıklarla)
    let finalMessages = [];

    while (!isCompleted && attempts < maxAttempts) {
      const runStatusResponse = await fetch(
        `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/runs/${runData.id}?api-version=${version}`,
        {
          headers: {
            "api-key": apiConfig.apiKey,
          },
        }
      );

      if (!runStatusResponse.ok) {
        throw new Error("Failed to check run status");
      }

      const runStatus = await runStatusResponse.json();

      if (runStatus.status === "completed") {
        // Mesajları al
        const messagesResponse = await fetch(
          `https://${instanceName}.openai.azure.com/openai/threads/${threadId}/messages?api-version=${version}`,
          {
            headers: {
              "api-key": apiConfig.apiKey,
            },
          }
        );

        if (!messagesResponse.ok) {
          throw new Error("Failed to fetch messages");
        }

        const messagesData = await messagesResponse.json();
        finalMessages = messagesData.data;
        isCompleted = true;
      } else if (runStatus.status === "failed") {
        throw new Error(
          "Run failed: " + (runStatus.last_error?.message || "Unknown error")
        );
      }

      if (!isCompleted) {
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, 5000)); // 5 saniye bekle
      }
    }

    if (!isCompleted) {
      throw new Error("Run timed out");
    }

    // Assistant'ın son mesajını bul
    const assistantMessage = finalMessages.find(
      (msg) => msg.role === "assistant"
    );

    if (!assistantMessage) {
      throw new Error("No assistant message found");
    }

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Assistant response generated successfully",
      {
        content: assistantMessage.content[0].text.value,
        threadId: threadId,
        runId: runData.id,
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Assistant failed",
      { message: error.message }
    );
  }
};

// Cache durumunu kontrol eden yeni endpoint
exports.getCacheStatus = async (req, res) => {
  try {
    const cacheStats = avatarCache.getStats();
    const cachedAvatars = avatarCache.get("heygen_avatars");

    return httpResponse(
      res,
      ENUM.HTTP_CODES.OK,
      "success",
      "Cache status fetched successfully",
      {
        stats: cacheStats,
        hasAvatars: !!cachedAvatars,
        avatarCount: cachedAvatars ? cachedAvatars.length : 0,
        timeToExpire: avatarCache.getTtl("heygen_avatars"),
      }
    );
  } catch (error) {
    return httpResponse(
      res,
      ENUM.HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "Failed to fetch cache status",
      error
    );
  }
};
